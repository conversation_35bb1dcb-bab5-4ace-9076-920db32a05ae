{"name": "@intlify/core-base", "version": "9.14.4", "description": "@intlify/core-base", "keywords": ["core", "fundamental", "i18n", "internationalization", "intlify"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n/tree/master/packages/core-base#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/intlify/vue-i18n/issues"}, "files": ["index.js", "dist"], "main": "index.js", "module": "dist/core-base.mjs", "unpkg": "dist/core-base.global.js", "jsdelivr": "dist/core-base.global.js", "types": "dist/core-base.d.ts", "dependencies": {"@intlify/message-compiler": "9.14.4", "@intlify/shared": "9.14.4"}, "devDependencies": {"@intlify/vue-devtools": "9.14.4", "@intlify/devtools-if": "9.14.4"}, "engines": {"node": ">= 16"}, "buildOptions": {"name": "IntlifyCoreBase", "formats": ["mjs", "browser", "cjs", "global"]}, "exports": {".": {"types": "./dist/core-base.d.ts", "import": "./dist/core-base.mjs", "browser": "./dist/core-base.esm-browser.js", "node": {"import": {"production": "./dist/core-base.prod.cjs", "development": "./dist/core-base.mjs", "default": "./dist/core-base.mjs"}, "require": {"production": "./dist/core-base.prod.cjs", "development": "./dist/core-base.cjs", "default": "./index.js"}}}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "funding": "https://github.com/sponsors/kazupon", "publishConfig": {"access": "public"}, "sideEffects": false}