<template>
  <footer class="bg-gray-900 text-white">
    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Logo and Description -->
        <div class="col-span-1 md:col-span-2">
          <div class="flex items-center mb-4">
            <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
              <Code2Icon class="w-5 h-5 text-white" />
            </div>
            <span class="ml-2 text-xl font-bold text-white">流码 FLOWCODE</span>
          </div>
          <p class="text-gray-300 mb-4 max-w-md">
            下一代AI编程助手，让代码编写变得更加智能、高效。通过先进的人工智能技术，为开发者提供全方位的编程支持。
          </p>
          <div class="flex space-x-4">
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <GithubIcon class="w-5 h-5" />
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <TwitterIcon class="w-5 h-5" />
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <LinkedinIcon class="w-5 h-5" />
            </a>
          </div>
        </div>

        <!-- Product Links -->
        <div>
          <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase mb-4">产品</h3>
          <ul class="space-y-2">
            <li><a href="#" class="text-gray-300 hover:text-white transition-colors">功能特性</a></li>
            <li><a href="#" class="text-gray-300 hover:text-white transition-colors">价格方案</a></li>
            <li><a href="#" class="text-gray-300 hover:text-white transition-colors">API文档</a></li>
            <li><a href="#" class="text-gray-300 hover:text-white transition-colors">集成指南</a></li>
          </ul>
        </div>

        <!-- Company Links -->
        <div>
          <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase mb-4">公司</h3>
          <ul class="space-y-2">
            <li><a href="#" class="text-gray-300 hover:text-white transition-colors">关于我们</a></li>
            <li><a href="#" class="text-gray-300 hover:text-white transition-colors">博客</a></li>
            <li><a href="#" class="text-gray-300 hover:text-white transition-colors">招聘</a></li>
            <li><a href="#" class="text-gray-300 hover:text-white transition-colors">联系我们</a></li>
          </ul>
        </div>
      </div>

      <div class="mt-8 pt-8 border-t border-gray-800">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <p class="text-gray-400 text-sm">
            © 2024 流码 FLOWCODE. 保留所有权利。
          </p>
          <div class="flex space-x-6 mt-4 md:mt-0">
            <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">隐私政策</a>
            <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">服务条款</a>
            <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Cookie政策</a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { Code2, Github, Twitter, Linkedin } from 'lucide-vue-next'

const Code2Icon = Code2
const GithubIcon = Github
const TwitterIcon = Twitter
const LinkedinIcon = Linkedin
</script>
