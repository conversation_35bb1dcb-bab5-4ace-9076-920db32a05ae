<template>
  <section class="relative overflow-hidden gradient-bg">
    <!-- Background decorations -->
    <div class="absolute inset-0">
      <div class="absolute top-0 left-0 w-72 h-72 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-slow"></div>
      <div class="absolute top-0 right-0 w-72 h-72 bg-accent-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-slow animation-delay-2000"></div>
      <div class="absolute bottom-0 left-1/2 w-72 h-72 bg-secondary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-slow animation-delay-4000"></div>
    </div>

    <div class="relative max-w-7xl mx-auto section-padding">
      <div class="text-center">
        <!-- Main heading -->
        <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 animate-fade-in">
          <span class="gradient-text">{{ $t('hero.title') }}</span>
          <br />
          <span class="text-gray-900">{{ $t('hero.subtitle') }}</span>
        </h1>

        <!-- Subtitle -->
        <p class="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto animate-slide-up">
          {{ $t('hero.description') }}
        </p>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-slide-up">
          <button class="btn-primary text-lg px-8 py-4">
            <PlayIcon class="w-5 h-5 mr-2" />
            {{ $t('hero.tryNow') }}
          </button>
          <button class="btn-secondary text-lg px-8 py-4">
            <BookOpenIcon class="w-5 h-5 mr-2" />
            {{ $t('hero.viewDocs') }}
          </button>
        </div>

        <!-- Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto animate-fade-in">
          <div class="text-center">
            <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">100K+</div>
            <div class="text-gray-600">{{ $t('hero.stats.developers') }}</div>
          </div>
          <div class="text-center">
            <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">1M+</div>
            <div class="text-gray-600">{{ $t('hero.stats.generations') }}</div>
          </div>
          <div class="text-center">
            <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">50+</div>
            <div class="text-gray-600">{{ $t('hero.stats.languages') }}</div>
          </div>
          <div class="text-center">
            <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">99.9%</div>
            <div class="text-gray-600">{{ $t('hero.stats.uptime') }}</div>
          </div>
        </div>
      </div>

      <!-- Demo/Preview Section -->
      <div class="mt-16 animate-slide-up">
        <div class="max-w-5xl mx-auto">
          <div class="bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-200">
            <!-- Terminal Header -->
            <div class="bg-gray-800 px-6 py-4 flex items-center">
              <div class="flex space-x-2">
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
              <div class="flex-1 text-center">
                <span class="text-gray-300 text-sm font-mono">流码 FLOWCODE - AI编程助手</span>
              </div>
            </div>
            
            <!-- Code Demo -->
            <div class="bg-gray-900 text-green-400 p-6 font-mono text-sm overflow-x-auto">
              <div class="space-y-2">
                <div class="flex">
                  <span class="text-blue-400">$</span>
                  <span class="ml-2 text-white">flowcode generate --task "创建一个用户登录API"</span>
                </div>
                <div class="text-gray-400">🤖 AI正在分析需求...</div>
                <div class="text-gray-400">✨ 生成代码结构...</div>
                <div class="text-gray-400">🔧 优化代码质量...</div>
                <div class="text-green-400">✅ 代码生成完成！</div>
                <div class="mt-4 text-white">
                  <div class="text-yellow-400">// 生成的登录API代码</div>
                  <div><span class="text-purple-400">export</span> <span class="text-blue-400">async function</span> <span class="text-yellow-400">loginUser</span>(<span class="text-orange-400">credentials</span>) {</div>
                  <div class="ml-4"><span class="text-gray-400">// AI自动生成的安全登录逻辑</span></div>
                  <div class="ml-4"><span class="text-purple-400">const</span> <span class="text-blue-400">result</span> = <span class="text-purple-400">await</span> <span class="text-yellow-400">authenticate</span>(<span class="text-orange-400">credentials</span>);</div>
                  <div class="ml-4"><span class="text-purple-400">return</span> <span class="text-blue-400">result</span>;</div>
                  <div>}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { Play, BookOpen } from 'lucide-vue-next'

const PlayIcon = Play
const BookOpenIcon = BookOpen
</script>

<style scoped>
.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}
</style>
