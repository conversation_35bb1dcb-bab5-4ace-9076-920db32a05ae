<template>
  <section id="features" class="section-padding bg-white">
    <div class="max-w-7xl mx-auto">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          强大的AI编程功能
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          流码FLOWCODE集成了最先进的AI技术，为开发者提供全方位的编程支持，让代码编写变得更加智能高效
        </p>
      </div>

      <!-- Features Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Feature 1: Code Generation -->
        <div class="card group hover:scale-105">
          <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
            <CodeIcon class="w-6 h-6 text-white" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">智能代码生成</h3>
          <p class="text-gray-600 mb-4">
            基于自然语言描述，AI自动生成高质量、符合最佳实践的代码，支持50+编程语言
          </p>
          <ul class="text-sm text-gray-500 space-y-1">
            <li>• 自然语言转代码</li>
            <li>• 多语言支持</li>
            <li>• 最佳实践遵循</li>
          </ul>
        </div>

        <!-- Feature 2: Bug Detection -->
        <div class="card group hover:scale-105">
          <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
            <BugIcon class="w-6 h-6 text-white" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">实时错误检测</h3>
          <p class="text-gray-600 mb-4">
            AI实时分析代码，提前发现潜在问题，提供智能修复建议，大幅提升代码质量
          </p>
          <ul class="text-sm text-gray-500 space-y-1">
            <li>• 实时代码分析</li>
            <li>• 智能修复建议</li>
            <li>• 性能优化提示</li>
          </ul>
        </div>

        <!-- Feature 3: Auto Testing -->
        <div class="card group hover:scale-105">
          <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
            <TestTubeIcon class="w-6 h-6 text-white" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">自动化测试</h3>
          <p class="text-gray-600 mb-4">
            AI自动生成全面的测试用例，包括单元测试、集成测试，确保代码的可靠性和稳定性
          </p>
          <ul class="text-sm text-gray-500 space-y-1">
            <li>• 自动生成测试用例</li>
            <li>• 覆盖率分析</li>
            <li>• 持续集成支持</li>
          </ul>
        </div>

        <!-- Feature 4: Code Review -->
        <div class="card group hover:scale-105">
          <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
            <EyeIcon class="w-6 h-6 text-white" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">智能代码审查</h3>
          <p class="text-gray-600 mb-4">
            AI助手提供专业的代码审查，检查代码规范、安全漏洞、性能问题，提升团队协作效率
          </p>
          <ul class="text-sm text-gray-500 space-y-1">
            <li>• 代码规范检查</li>
            <li>• 安全漏洞扫描</li>
            <li>• 团队协作优化</li>
          </ul>
        </div>

        <!-- Feature 5: Documentation -->
        <div class="card group hover:scale-105">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
            <FileTextIcon class="w-6 h-6 text-white" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">自动文档生成</h3>
          <p class="text-gray-600 mb-4">
            AI自动分析代码结构和功能，生成详细的技术文档和API文档，保持文档与代码同步
          </p>
          <ul class="text-sm text-gray-500 space-y-1">
            <li>• API文档自动生成</li>
            <li>• 代码注释优化</li>
            <li>• 文档版本管理</li>
          </ul>
        </div>

        <!-- Feature 6: Performance -->
        <div class="card group hover:scale-105">
          <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
            <ZapIcon class="w-6 h-6 text-white" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">性能优化</h3>
          <p class="text-gray-600 mb-4">
            AI分析代码性能瓶颈，提供优化建议和重构方案，帮助开发者写出更高效的代码
          </p>
          <ul class="text-sm text-gray-500 space-y-1">
            <li>• 性能瓶颈分析</li>
            <li>• 优化建议提供</li>
            <li>• 代码重构指导</li>
          </ul>
        </div>
      </div>

      <!-- Integration Section -->
      <div class="mt-16 text-center">
        <h3 class="text-2xl font-bold text-gray-900 mb-8">无缝集成你喜爱的开发工具</h3>
        <div class="flex flex-wrap justify-center items-center gap-8 opacity-60">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gray-800 rounded"></div>
            <span class="font-semibold">VS Code</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-blue-600 rounded"></div>
            <span class="font-semibold">IntelliJ</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-green-600 rounded"></div>
            <span class="font-semibold">Sublime</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-purple-600 rounded"></div>
            <span class="font-semibold">Vim</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-orange-600 rounded"></div>
            <span class="font-semibold">Atom</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { Code, Bug, TestTube, Eye, FileText, Zap } from 'lucide-vue-next'

const CodeIcon = Code
const BugIcon = Bug
const TestTubeIcon = TestTube
const EyeIcon = Eye
const FileTextIcon = FileText
const ZapIcon = Zap
</script>
