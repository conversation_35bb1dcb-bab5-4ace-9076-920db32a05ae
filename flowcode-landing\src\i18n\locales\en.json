{"nav": {"features": "Features", "pricing": "Pricing", "about": "About", "contact": "Contact", "getStarted": "Get Started"}, "hero": {"title": "FLOWCODE", "subtitle": "Next-Generation AI Programming Assistant", "description": "Make AI your programming partner. Intelligent code generation, real-time error fixing, automated testing - making programming more efficient, intelligent, and enjoyable", "tryNow": "Try Now", "viewDocs": "View Docs", "stats": {"developers": "Active Developers", "generations": "Code Generations", "languages": "Programming Languages", "uptime": "Service Uptime"}}, "features": {"title": "Powerful AI Programming Features", "subtitle": "FLOWCODE integrates cutting-edge AI technology to provide comprehensive programming support, making code development smarter and more efficient", "codeGeneration": {"title": "Intelligent Code Generation", "description": "Generate high-quality, best-practice code from natural language descriptions, supporting 50+ programming languages", "features": ["Natural Language to Code", "Multi-language Support", "Best Practice Compliance"]}, "bugDetection": {"title": "Real-time Error Detection", "description": "AI analyzes code in real-time, identifies potential issues early, and provides intelligent fix suggestions to dramatically improve code quality", "features": ["Real-time Code Analysis", "Smart Fix Suggestions", "Performance Optimization Tips"]}, "autoTesting": {"title": "Automated Testing", "description": "AI automatically generates comprehensive test cases, including unit tests and integration tests, ensuring code reliability and stability", "features": ["Auto-generate Test Cases", "Coverage Analysis", "CI/CD Support"]}, "codeReview": {"title": "Intelligent Code Review", "description": "AI assistant provides professional code reviews, checking code standards, security vulnerabilities, and performance issues to enhance team collaboration", "features": ["Code Standard Checks", "Security Vulnerability Scanning", "Team Collaboration Optimization"]}, "documentation": {"title": "Automatic Documentation", "description": "AI automatically analyzes code structure and functionality to generate detailed technical and API documentation, keeping docs in sync with code", "features": ["Auto API Documentation", "Code Comment Optimization", "Document Version Control"]}, "performance": {"title": "Performance Optimization", "description": "AI analyzes code performance bottlenecks, provides optimization suggestions and refactoring solutions to help developers write more efficient code", "features": ["Performance Bottleneck Analysis", "Optimization Suggestions", "Code Refactoring Guidance"]}, "integration": "Seamlessly integrate with your favorite development tools"}, "testimonials": {"title": "Real Feedback from Developers", "subtitle": "Experience from 100K+ developers worldwide - see how they boost programming efficiency with FLOWCODE", "reviews": [{"content": "FLOWCODE has completely transformed my programming approach. The AI code generation feature boosted my development efficiency by 300%, and the generated code quality is so high it barely needs modification.", "author": "<PERSON>", "position": "Full-stack Developer @ Tencent"}, {"content": "As a team lead, I love the intelligent code review feature most. It helped us discover many potential security issues and greatly improved code quality and team collaboration efficiency.", "author": "<PERSON>", "position": "Technical Director @ ByteDance"}, {"content": "The automated testing feature is amazing! Writing test cases used to take so much time, now AI-generated tests achieve 95%+ coverage, saving tremendous time.", "author": "<PERSON>", "position": "Backend Engineer @ Alibaba"}, {"content": "As a beginner, FLOWCODE is like my programming mentor. It not only helps me write code but also teaches best practices, helping me quickly grow into a competent developer.", "author": "<PERSON>", "position": "Frontend Engineer @ Meituan"}, {"content": "The performance optimization feature helped us solve many production issues. AI can accurately identify performance bottlenecks and provide specific optimization solutions - unimaginable before.", "author": "<PERSON>", "position": "Architect @ JD.com"}, {"content": "The automatic documentation feature is so practical! Documentation used to be my biggest headache, now AI-generated docs are both detailed and accurate, greatly reducing our workload.", "author": "<PERSON>", "position": "Product Manager @ Xiaomi"}], "stats": {"rating": "User Rating", "satisfaction": "User Satisfaction", "efficiency": "Efficiency Boost", "support": "Technical Support"}}, "pricing": {"title": "Choose Your Perfect Plan", "subtitle": "From individual developers to enterprise teams, we offer flexible pricing plans so everyone can enjoy the convenience of AI programming", "plans": {"free": {"name": "Free", "description": "Perfect for personal learning and small projects", "price": "$0", "period": "/month", "button": "Get Started", "features": ["100 code generations per month", "Basic error detection", "5 programming languages", "Community support"], "unavailable": ["Automated testing", "Code review"]}, "pro": {"name": "Professional", "description": "Perfect for professional developers and small teams", "price": "$15", "period": "/month", "button": "Upgrade Now", "popular": "Most Popular", "features": ["Unlimited code generation", "Advanced error detection & fixes", "50+ programming languages", "Automated test generation", "Intelligent code review", "Priority technical support"]}, "enterprise": {"name": "Enterprise", "description": "Perfect for large teams and enterprises", "price": "$45", "period": "/month", "button": "Contact Sales", "features": ["All Professional features", "Team collaboration management", "Private deployment options", "Custom AI models", "Enterprise-grade security", "24/7 dedicated support"]}}, "faq": {"title": "Frequently Asked Questions", "questions": [{"question": "Can I cancel my subscription anytime?", "answer": "Absolutely! You can cancel your subscription anytime in account settings. You'll continue to have access until the end of your current billing period."}, {"question": "Which programming languages are supported?", "answer": "We support 50+ mainstream programming languages including Python, JavaScript, Java, C++, Go, Rust, and more, with continuous addition of new language support."}, {"question": "How is data security ensured?", "answer": "We use enterprise-grade encryption technology. All code data is encrypted during transmission and storage, and we never leak your business secrets."}, {"question": "Is there a free trial period?", "answer": "Both Professional and Enterprise plans offer a 14-day free trial with no credit card required, letting you fully experience all premium features."}]}}, "cta": {"title": "Ready to let AI transform your", "titleHighlight": "programming approach?", "description": "Join 100K+ developers and experience the efficiency revolution brought by next-generation AI programming assistants. Start now and make programming smarter, more efficient, and more enjoyable!", "startFree": "Start Free Trial", "bookDemo": "Book Demo", "trust": {"freeTrial": "14-day free trial", "noCard": "No credit card required", "cancelAnytime": "Cancel anytime"}, "newsletter": {"title": "Subscribe to Our Tech Newsletter", "description": "Get the latest AI programming tips, product updates, and industry insights", "placeholder": "Enter your email address", "subscribe": "Subscribe", "disclaimer": "We promise not to send spam, and you can unsubscribe anytime"}}, "footer": {"description": "Next-generation AI programming assistant making code development smarter and more efficient. Providing comprehensive programming support through advanced artificial intelligence technology.", "product": "Product", "company": "Company", "links": {"features": "Features", "pricing": "Pricing", "docs": "API Docs", "integration": "Integration Guide", "about": "About Us", "blog": "Blog", "careers": "Careers", "contact": "Contact Us"}, "legal": {"copyright": "© 2024 FLOWCODE. All rights reserved.", "privacy": "Privacy Policy", "terms": "Terms of Service", "cookies": "<PERSON><PERSON>"}}}