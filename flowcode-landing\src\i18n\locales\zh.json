{"nav": {"features": "功能特性", "pricing": "价格方案", "about": "关于我们", "contact": "联系我们", "getStarted": "开始体验"}, "hero": {"title": "流码 FLOWCODE", "subtitle": "下一代AI编程助手", "description": "让AI成为你的编程伙伴，智能代码生成、实时错误修复、自动化测试，让编程变得更加高效、智能、有趣", "tryNow": "立即体验", "viewDocs": "查看文档", "stats": {"developers": "活跃开发者", "generations": "代码生成次数", "languages": "编程语言支持", "uptime": "服务可用性"}}, "features": {"title": "强大的AI编程功能", "subtitle": "流码FLOWCODE集成了最先进的AI技术，为开发者提供全方位的编程支持，让代码编写变得更加智能高效", "codeGeneration": {"title": "智能代码生成", "description": "基于自然语言描述，AI自动生成高质量、符合最佳实践的代码，支持50+编程语言", "features": ["自然语言转代码", "多语言支持", "最佳实践遵循"]}, "bugDetection": {"title": "实时错误检测", "description": "AI实时分析代码，提前发现潜在问题，提供智能修复建议，大幅提升代码质量", "features": ["实时代码分析", "智能修复建议", "性能优化提示"]}, "autoTesting": {"title": "自动化测试", "description": "AI自动生成全面的测试用例，包括单元测试、集成测试，确保代码的可靠性和稳定性", "features": ["自动生成测试用例", "覆盖率分析", "持续集成支持"]}, "codeReview": {"title": "智能代码审查", "description": "AI助手提供专业的代码审查，检查代码规范、安全漏洞、性能问题，提升团队协作效率", "features": ["代码规范检查", "安全漏洞扫描", "团队协作优化"]}, "documentation": {"title": "自动文档生成", "description": "AI自动分析代码结构和功能，生成详细的技术文档和API文档，保持文档与代码同步", "features": ["API文档自动生成", "代码注释优化", "文档版本管理"]}, "performance": {"title": "性能优化", "description": "AI分析代码性能瓶颈，提供优化建议和重构方案，帮助开发者写出更高效的代码", "features": ["性能瓶颈分析", "优化建议提供", "代码重构指导"]}, "integration": "无缝集成你喜爱的开发工具"}, "testimonials": {"title": "开发者们的真实反馈", "subtitle": "来自全球10万+开发者的使用体验，看看他们如何通过流码FLOWCODE提升编程效率", "reviews": [{"content": "流码FLOWCODE彻底改变了我的编程方式。AI代码生成功能让我的开发效率提升了300%，而且生成的代码质量非常高，几乎不需要修改。", "author": "张伟", "position": "全栈开发工程师 @ 腾讯"}, {"content": "作为团队负责人，我最喜欢的是智能代码审查功能。它帮助我们发现了很多潜在的安全问题，大大提升了代码质量和团队协作效率。", "author": "<PERSON>小明", "position": "技术总监 @ 字节跳动"}, {"content": "自动化测试功能太棒了！以前写测试用例要花很多时间，现在AI自动生成的测试覆盖率达到95%以上，节省了大量时间。", "author": "王小红", "position": "后端开发工程师 @ 阿里巴巴"}, {"content": "作为初学者，流码FLOWCODE就像我的编程导师。它不仅帮我写代码，还教我最佳实践，让我快速成长为一名合格的开发者。", "author": "陈小华", "position": "前端开发工程师 @ 美团"}, {"content": "性能优化功能帮我们解决了很多线上问题。AI能准确识别性能瓶颈并提供具体的优化方案，这在以前是不可想象的。", "author": "刘大强", "position": "架构师 @ 京东"}, {"content": "文档自动生成功能太实用了！以前最头疼的就是写文档，现在AI自动生成的文档既详细又准确，大大减轻了我们的工作负担。", "author": "赵小丽", "position": "产品经理 @ 小米"}], "stats": {"rating": "用户评分", "satisfaction": "用户满意度", "efficiency": "效率提升", "support": "技术支持"}}, "pricing": {"title": "选择适合你的方案", "subtitle": "从个人开发者到企业团队，我们提供灵活的定价方案，让每个人都能享受AI编程的便利", "plans": {"free": {"name": "免费版", "description": "适合个人学习和小型项目", "price": "¥0", "period": "/月", "button": "立即开始", "features": ["每月100次代码生成", "基础错误检测", "5种编程语言支持", "社区支持"], "unavailable": ["自动化测试", "代码审查"]}, "pro": {"name": "专业版", "description": "适合专业开发者和小团队", "price": "¥99", "period": "/月", "button": "立即升级", "popular": "最受欢迎", "features": ["无限代码生成", "高级错误检测与修复", "50+编程语言支持", "自动化测试生成", "智能代码审查", "优先技术支持"]}, "enterprise": {"name": "企业版", "description": "适合大型团队和企业", "price": "¥299", "period": "/月", "button": "联系销售", "features": ["专业版所有功能", "团队协作管理", "私有部署选项", "定制化AI模型", "企业级安全保障", "24/7专属客服"]}}, "faq": {"title": "常见问题", "questions": [{"question": "可以随时取消订阅吗？", "answer": "当然可以！您可以随时在账户设置中取消订阅，取消后仍可使用到当前计费周期结束。"}, {"question": "支持哪些编程语言？", "answer": "我们支持50+主流编程语言，包括Python、JavaScript、Java、C++、Go、Rust等，并持续增加新的语言支持。"}, {"question": "数据安全如何保障？", "answer": "我们采用企业级加密技术，所有代码数据都经过加密传输和存储，绝不会泄露您的商业机密。"}, {"question": "有免费试用期吗？", "answer": "专业版和企业版都提供14天免费试用，无需信用卡，让您充分体验所有高级功能。"}]}}, "cta": {"title": "准备好让AI改变你的", "titleHighlight": "编程方式了吗？", "description": "加入10万+开发者的行列，体验下一代AI编程助手带来的效率革命。立即开始，让编程变得更加智能、高效、有趣！", "startFree": "免费开始体验", "bookDemo": "预约演示", "trust": {"freeTrial": "14天免费试用", "noCard": "无需信用卡", "cancelAnytime": "随时取消"}, "newsletter": {"title": "订阅我们的技术资讯", "description": "获取最新的AI编程技巧、产品更新和行业洞察", "placeholder": "输入您的邮箱地址", "subscribe": "订阅", "disclaimer": "我们承诺不会发送垃圾邮件，您可以随时取消订阅"}}, "footer": {"description": "下一代AI编程助手，让代码编写变得更加智能、高效。通过先进的人工智能技术，为开发者提供全方位的编程支持。", "product": "产品", "company": "公司", "links": {"features": "功能特性", "pricing": "价格方案", "docs": "API文档", "integration": "集成指南", "about": "关于我们", "blog": "博客", "careers": "招聘", "contact": "联系我们"}, "legal": {"copyright": "© 2024 流码 FLOWCODE. 保留所有权利。", "privacy": "隐私政策", "terms": "服务条款", "cookies": "<PERSON><PERSON>政策"}}}