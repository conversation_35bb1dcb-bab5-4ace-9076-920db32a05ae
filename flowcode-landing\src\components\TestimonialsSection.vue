<template>
  <section class="section-padding gradient-bg">
    <div class="max-w-7xl mx-auto">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          开发者们的真实反馈
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          来自全球10万+开发者的使用体验，看看他们如何通过流码FLOWCODE提升编程效率
        </p>
      </div>

      <!-- Testimonials Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Testimonial 1 -->
        <div class="card">
          <div class="flex items-center mb-4">
            <div class="flex text-yellow-400">
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
            </div>
          </div>
          <p class="text-gray-600 mb-6">
            "流码FLOWCODE彻底改变了我的编程方式。AI代码生成功能让我的开发效率提升了300%，而且生成的代码质量非常高，几乎不需要修改。"
          </p>
          <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">
              张
            </div>
            <div class="ml-4">
              <div class="font-semibold text-gray-900">张伟</div>
              <div class="text-sm text-gray-500">全栈开发工程师 @ 腾讯</div>
            </div>
          </div>
        </div>

        <!-- Testimonial 2 -->
        <div class="card">
          <div class="flex items-center mb-4">
            <div class="flex text-yellow-400">
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
            </div>
          </div>
          <p class="text-gray-600 mb-6">
            "作为团队负责人，我最喜欢的是智能代码审查功能。它帮助我们发现了很多潜在的安全问题，大大提升了代码质量和团队协作效率。"
          </p>
          <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-500 rounded-full flex items-center justify-center text-white font-bold">
              李
            </div>
            <div class="ml-4">
              <div class="font-semibold text-gray-900">李小明</div>
              <div class="text-sm text-gray-500">技术总监 @ 字节跳动</div>
            </div>
          </div>
        </div>

        <!-- Testimonial 3 -->
        <div class="card">
          <div class="flex items-center mb-4">
            <div class="flex text-yellow-400">
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
            </div>
          </div>
          <p class="text-gray-600 mb-6">
            "自动化测试功能太棒了！以前写测试用例要花很多时间，现在AI自动生成的测试覆盖率达到95%以上，节省了大量时间。"
          </p>
          <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-pink-500 to-red-500 rounded-full flex items-center justify-center text-white font-bold">
              王
            </div>
            <div class="ml-4">
              <div class="font-semibold text-gray-900">王小红</div>
              <div class="text-sm text-gray-500">后端开发工程师 @ 阿里巴巴</div>
            </div>
          </div>
        </div>

        <!-- Testimonial 4 -->
        <div class="card">
          <div class="flex items-center mb-4">
            <div class="flex text-yellow-400">
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
            </div>
          </div>
          <p class="text-gray-600 mb-6">
            "作为初学者，流码FLOWCODE就像我的编程导师。它不仅帮我写代码，还教我最佳实践，让我快速成长为一名合格的开发者。"
          </p>
          <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
              陈
            </div>
            <div class="ml-4">
              <div class="font-semibold text-gray-900">陈小华</div>
              <div class="text-sm text-gray-500">前端开发工程师 @ 美团</div>
            </div>
          </div>
        </div>

        <!-- Testimonial 5 -->
        <div class="card">
          <div class="flex items-center mb-4">
            <div class="flex text-yellow-400">
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
            </div>
          </div>
          <p class="text-gray-600 mb-6">
            "性能优化功能帮我们解决了很多线上问题。AI能准确识别性能瓶颈并提供具体的优化方案，这在以前是不可想象的。"
          </p>
          <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-full flex items-center justify-center text-white font-bold">
              刘
            </div>
            <div class="ml-4">
              <div class="font-semibold text-gray-900">刘大强</div>
              <div class="text-sm text-gray-500">架构师 @ 京东</div>
            </div>
          </div>
        </div>

        <!-- Testimonial 6 -->
        <div class="card">
          <div class="flex items-center mb-4">
            <div class="flex text-yellow-400">
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
              <StarIcon class="w-5 h-5" />
            </div>
          </div>
          <p class="text-gray-600 mb-6">
            "文档自动生成功能太实用了！以前最头疼的就是写文档，现在AI自动生成的文档既详细又准确，大大减轻了我们的工作负担。"
          </p>
          <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold">
              赵
            </div>
            <div class="ml-4">
              <div class="font-semibold text-gray-900">赵小丽</div>
              <div class="text-sm text-gray-500">产品经理 @ 小米</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Stats Section -->
      <div class="mt-16 text-center">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
          <div>
            <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">4.9/5</div>
            <div class="text-gray-600">用户评分</div>
          </div>
          <div>
            <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">98%</div>
            <div class="text-gray-600">用户满意度</div>
          </div>
          <div>
            <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">300%</div>
            <div class="text-gray-600">效率提升</div>
          </div>
          <div>
            <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">24/7</div>
            <div class="text-gray-600">技术支持</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { Star } from 'lucide-vue-next'

const StarIcon = Star
</script>
