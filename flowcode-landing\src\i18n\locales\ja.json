{"nav": {"features": "機能", "pricing": "料金プラン", "about": "会社概要", "contact": "お問い合わせ", "getStarted": "始める"}, "hero": {"title": "FLOWCODE", "subtitle": "次世代AIプログラミングアシスタント", "description": "AIをプログラミングパートナーに。インテリジェントなコード生成、リアルタイムエラー修正、自動テストで、プログラミングをより効率的で、インテリジェントで、楽しいものに", "tryNow": "今すぐ試す", "viewDocs": "ドキュメント", "stats": {"developers": "アクティブ開発者", "generations": "コード生成回数", "languages": "プログラミング言語", "uptime": "サービス稼働率"}}, "features": {"title": "強力なAIプログラミング機能", "subtitle": "FLOWCODEは最先端のAI技術を統合し、包括的なプログラミングサポートを提供し、コード開発をよりスマートで効率的にします", "codeGeneration": {"title": "インテリジェントコード生成", "description": "自然言語の説明から高品質でベストプラクティスに準拠したコードを生成、50以上のプログラミング言語をサポート", "features": ["自然言語からコードへ", "多言語サポート", "ベストプラクティス準拠"]}, "bugDetection": {"title": "リアルタイムエラー検出", "description": "AIがリアルタイムでコードを分析し、潜在的な問題を早期に特定し、インテリジェントな修正提案を提供してコード品質を大幅に向上", "features": ["リアルタイムコード分析", "スマート修正提案", "パフォーマンス最適化のヒント"]}, "autoTesting": {"title": "自動テスト", "description": "AIが包括的なテストケースを自動生成、ユニットテストや統合テストを含み、コードの信頼性と安定性を確保", "features": ["テストケース自動生成", "カバレッジ分析", "CI/CDサポート"]}, "codeReview": {"title": "インテリジェントコードレビュー", "description": "AIアシスタントがプロフェッショナルなコードレビューを提供、コード標準、セキュリティ脆弱性、パフォーマンス問題をチェックしてチーム協力を向上", "features": ["コード標準チェック", "セキュリティ脆弱性スキャン", "チーム協力最適化"]}, "documentation": {"title": "自動ドキュメント生成", "description": "AIがコード構造と機能を自動分析し、詳細な技術文書とAPIドキュメントを生成、ドキュメントとコードの同期を維持", "features": ["API文書自動生成", "コードコメント最適化", "文書バージョン管理"]}, "performance": {"title": "パフォーマンス最適化", "description": "AIがコードのパフォーマンスボトルネックを分析し、最適化提案とリファクタリングソリューションを提供して、開発者がより効率的なコードを書けるよう支援", "features": ["パフォーマンスボトルネック分析", "最適化提案", "コードリファクタリングガイダンス"]}, "integration": "お気に入りの開発ツールとシームレスに統合"}, "testimonials": {"title": "開発者からの実際のフィードバック", "subtitle": "世界中の10万人以上の開発者の使用体験 - FLOWCODEでプログラミング効率をどのように向上させているかをご覧ください", "reviews": [{"content": "FLOWCODEは私のプログラミングアプローチを完全に変えました。AIコード生成機能により開発効率が300%向上し、生成されるコードの品質が非常に高く、ほとんど修正が不要です。", "author": "張 偉", "position": "フルスタック開発者 @ Tencent"}, {"content": "チームリーダーとして、インテリジェントコードレビュー機能が最も気に入っています。多くの潜在的なセキュリティ問題を発見し、コード品質とチーム協力効率を大幅に向上させました。", "author": "<PERSON> 小明", "position": "技術ディレクター @ ByteDance"}, {"content": "自動テスト機能は素晴らしいです！テストケースを書くのに多くの時間がかかっていましたが、今ではAI生成テストが95%以上のカバレッジを達成し、膨大な時間を節約できます。", "author": "王 小紅", "position": "バックエンドエンジニア @ Alibaba"}, {"content": "初心者として、FLOWCODEは私のプログラミングメンターのようです。コードを書くのを手伝ってくれるだけでなく、ベストプラクティスも教えてくれ、有能な開発者として迅速に成長できました。", "author": "陳 小華", "position": "フロントエンドエンジニア @ Meituan"}, {"content": "パフォーマンス最適化機能により、多くの本番環境の問題を解決できました。AIはパフォーマンスボトルネックを正確に特定し、具体的な最適化ソリューションを提供します - 以前は想像できませんでした。", "author": "劉 大強", "position": "アーキテクト @ JD.com"}, {"content": "自動ドキュメント生成機能はとても実用的です！ドキュメント作成は最大の頭痛の種でしたが、今ではAI生成ドキュメントが詳細で正確で、作業負荷を大幅に軽減しました。", "author": "趙 小麗", "position": "プロダクトマネージャー @ Xiaomi"}], "stats": {"rating": "ユーザー評価", "satisfaction": "ユーザー満足度", "efficiency": "効率向上", "support": "技術サポート"}}, "pricing": {"title": "最適なプランを選択", "subtitle": "個人開発者から企業チームまで、柔軟な料金プランを提供し、誰もがAIプログラミングの便利さを享受できます", "plans": {"free": {"name": "無料版", "description": "個人学習と小規模プロジェクトに最適", "price": "¥0", "period": "/月", "button": "今すぐ始める", "features": ["月100回のコード生成", "基本エラー検出", "5つのプログラミング言語", "コミュニティサポート"], "unavailable": ["自動テスト", "コードレビュー"]}, "pro": {"name": "プロフェッショナル", "description": "プロの開発者と小チームに最適", "price": "¥1,980", "period": "/月", "button": "今すぐアップグレード", "popular": "最も人気", "features": ["無制限コード生成", "高度なエラー検出と修正", "50以上のプログラミング言語", "自動テスト生成", "インテリジェントコードレビュー", "優先技術サポート"]}, "enterprise": {"name": "エンタープライズ", "description": "大規模チームと企業に最適", "price": "¥5,980", "period": "/月", "button": "営業に連絡", "features": ["プロフェッショナル版の全機能", "チーム協力管理", "プライベート展開オプション", "カスタムAIモデル", "エンタープライズグレードセキュリティ", "24/7専用サポート"]}}, "faq": {"title": "よくある質問", "questions": [{"question": "いつでもサブスクリプションをキャンセルできますか？", "answer": "もちろんです！アカウント設定でいつでもサブスクリプションをキャンセルできます。現在の請求期間の終了まで引き続きアクセスできます。"}, {"question": "どのプログラミング言語がサポートされていますか？", "answer": "Python、JavaScript、Java、C++、Go、Rustなど50以上の主流プログラミング言語をサポートし、新しい言語サポートを継続的に追加しています。"}, {"question": "データセキュリティはどのように確保されていますか？", "answer": "エンタープライズグレードの暗号化技術を使用しています。すべてのコードデータは送信と保存時に暗号化され、お客様のビジネス機密を決して漏洩しません。"}, {"question": "無料試用期間はありますか？", "answer": "プロフェッショナル版とエンタープライズ版の両方で14日間の無料試用を提供しており、クレジットカードは不要で、すべてのプレミアム機能を十分に体験できます。"}]}}, "cta": {"title": "AIにあなたの", "titleHighlight": "プログラミングアプローチを変革させる準備はできていますか？", "description": "10万人以上の開発者に参加し、次世代AIプログラミングアシスタントがもたらす効率革命を体験してください。今すぐ始めて、プログラミングをよりスマートで、効率的で、楽しいものにしましょう！", "startFree": "無料トライアル開始", "bookDemo": "デモを予約", "trust": {"freeTrial": "14日間無料試用", "noCard": "クレジットカード不要", "cancelAnytime": "いつでもキャンセル"}, "newsletter": {"title": "技術ニュースレターを購読", "description": "最新のAIプログラミングのヒント、製品アップデート、業界インサイトを入手", "placeholder": "メールアドレスを入力", "subscribe": "購読", "disclaimer": "スパムメールは送信しないことをお約束し、いつでも購読解除できます"}}, "footer": {"description": "次世代AIプログラミングアシスタントで、コード開発をよりスマートで効率的に。先進的な人工知能技術により、開発者に包括的なプログラミングサポートを提供。", "product": "製品", "company": "会社", "links": {"features": "機能", "pricing": "料金プラン", "docs": "APIドキュメント", "integration": "統合ガイド", "about": "会社概要", "blog": "ブログ", "careers": "採用情報", "contact": "お問い合わせ"}, "legal": {"copyright": "© 2024 FLOWCODE. 全著作権所有。", "privacy": "プライバシーポリシー", "terms": "利用規約", "cookies": "Cookieポリシー"}}}