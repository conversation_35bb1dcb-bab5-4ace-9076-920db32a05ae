/*!
  * core-base v9.14.4
  * (c) 2025 ka<PERSON><PERSON>
  * Released under the MIT License.
  */
var IntlifyCoreBase=function(e){"use strict";const t=/\{([0-9a-zA-Z]+)\}/g;const n=(e,t,n)=>r({l:e,k:t,s:n}),r=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),o=e=>"number"==typeof e&&isFinite(e),c=e=>"[object Date]"===C(e),s=e=>"[object RegExp]"===C(e),a=e=>k(e)&&0===Object.keys(e).length,l=Object.assign,i=Object.create,u=(e=null)=>i(e);function f(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const E=Object.prototype.hasOwnProperty;function _(e,t){return E.call(e,t)}const m=Array.isArray,d=e=>"function"==typeof e,p=e=>"string"==typeof e,N=e=>"boolean"==typeof e,T=e=>null!==e&&"object"==typeof e,L=e=>T(e)&&d(e.then)&&d(e.catch),A=Object.prototype.toString,C=e=>A.call(e),k=e=>{if(!T(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t.constructor===Object};function h(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}function O(e){let t=e;return()=>++t}function I(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}function S(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const g={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2};const y={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17};function b(e,t,n={}){const{domain:r,messages:o,args:c}=n,s=new SyntaxError(String(e));return s.code=e,t&&(s.location=t),s.domain=r,s}function P(e){throw e}y.EXPECTED_TOKEN,y.INVALID_TOKEN_IN_PLACEHOLDER,y.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,y.UNKNOWN_ESCAPE_SEQUENCE,y.INVALID_UNICODE_ESCAPE_SEQUENCE,y.UNBALANCED_CLOSING_BRACE,y.UNTERMINATED_CLOSING_BRACE,y.EMPTY_PLACEHOLDER,y.NOT_ALLOW_NEST_PLACEHOLDER,y.INVALID_LINKED_FORMAT,y.MUST_HAVE_MESSAGES_IN_PLURAL,y.UNEXPECTED_EMPTY_LINKED_MODIFIER,y.UNEXPECTED_EMPTY_LINKED_KEY,y.UNEXPECTED_LEXICAL_ANALYSIS,y.UNHANDLED_CODEGEN_NODE_TYPE,y.UNHANDLED_MINIFIER_NODE_TYPE;const D=" ",M="\r",R="\n",U=String.fromCharCode(8232),v=String.fromCharCode(8233);function x(e){const t=e;let n=0,r=1,o=1,c=0;const s=e=>t[e]===M&&t[e+1]===R,a=e=>t[e]===v,l=e=>t[e]===U,i=e=>s(e)||(e=>t[e]===R)(e)||a(e)||l(e),u=e=>s(e)||a(e)||l(e)?R:t[e];function f(){return c=0,i(n)&&(r++,o=0),s(n)&&n++,n++,o++,t[n]}return{index:()=>n,line:()=>r,column:()=>o,peekOffset:()=>c,charAt:u,currentChar:()=>u(n),currentPeek:()=>u(n+c),next:f,peek:function(){return s(n+c)&&c++,c++,t[n+c]},reset:function(){n=0,r=1,o=1,c=0},resetPeek:function(e=0){c=e},skipToPeek:function(){const e=n+c;for(;e!==n;)f();c=0}}}const F=void 0,w="'";function W(e,t={}){const n=!1!==t.location,r=x(e),o=()=>r.index(),c=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},s=c(),a=o(),l={currentType:14,offset:a,startLoc:s,endLoc:s,lastType:14,lastOffset:a,lastStartLoc:s,lastEndLoc:s,braceNest:0,inLinked:!1,text:""},i=()=>l,{onError:u}=t;function f(e,t,r){e.endLoc=c(),e.currentType=t;const o={type:t};return n&&(o.loc=S(e.startLoc,e.endLoc)),null!=r&&(o.value=r),o}const E=e=>f(e,14);function _(e,t){return e.currentChar()===t?(e.next(),t):(y.EXPECTED_TOKEN,c(),"")}function m(e){let t="";for(;e.currentPeek()===D||e.currentPeek()===R;)t+=e.currentPeek(),e.peek();return t}function d(e){const t=m(e);return e.skipToPeek(),t}function p(e){if(e===F)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function N(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r=function(e){if(e===F)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function T(e){m(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function L(e,t=!0){const n=(t=!1,r="",o=!1)=>{const c=e.currentPeek();return"{"===c?"%"!==r&&t:"@"!==c&&c?"%"===c?(e.peek(),n(t,"%",!0)):"|"===c?!("%"!==r&&!o)||!(r===D||r===R):c===D?(e.peek(),n(!0,D,o)):c!==R||(e.peek(),n(!0,R,o)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function A(e,t){const n=e.currentChar();return n===F?F:t(n)?(e.next(),n):null}function C(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function k(e){return A(e,C)}function h(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function O(e){return A(e,h)}function I(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function g(e){return A(e,I)}function b(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function P(e){return A(e,b)}function M(e){let t="",n="";for(;t=g(e);)n+=t;return n}function U(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!L(e))break;t+=n,e.next()}else if(n===D||n===R)if(L(e))t+=n,e.next();else{if(T(e))break;t+=n,e.next()}else t+=n,e.next()}return t}function v(e){return e!==w&&e!==R}function W(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return X(e,t,4);case"U":return X(e,t,6);default:return y.UNKNOWN_ESCAPE_SEQUENCE,c(),""}}function X(e,t,n){_(e,t);let r="";for(let o=0;o<n;o++){const t=P(e);if(!t){y.INVALID_UNICODE_ESCAPE_SEQUENCE,c(),e.currentChar();break}r+=t}return`\\${t}${r}`}function Y(e){return"{"!==e&&"}"!==e&&e!==D&&e!==R}function K(e){d(e);const t=_(e,"|");return d(e),t}function G(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(y.NOT_ALLOW_NEST_PLACEHOLDER,c()),e.next(),n=f(t,2,"{"),d(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&(y.EMPTY_PLACEHOLDER,c()),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&d(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&(y.UNTERMINATED_CLOSING_BRACE,c()),n=$(e,t)||E(t),t.braceNest=0,n;default:{let r=!0,o=!0,s=!0;if(T(e))return t.braceNest>0&&(y.UNTERMINATED_CLOSING_BRACE,c()),n=f(t,1,K(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return y.UNTERMINATED_CLOSING_BRACE,c(),t.braceNest=0,V(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r=p(e.currentPeek());return e.resetPeek(),r}(e,t))return n=f(t,5,function(e){d(e);let t="",n="";for(;t=O(e);)n+=t;return e.currentChar()===F&&(y.UNTERMINATED_CLOSING_BRACE,c()),n}(e)),d(e),n;if(o=N(e,t))return n=f(t,6,function(e){d(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${M(e)}`):t+=M(e),e.currentChar()===F&&(y.UNTERMINATED_CLOSING_BRACE,c()),t}(e)),d(e),n;if(s=function(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r=e.currentPeek()===w;return e.resetPeek(),r}(e,t))return n=f(t,7,function(e){d(e),_(e,"'");let t="",n="";for(;t=A(e,v);)n+="\\"===t?W(e):t;const r=e.currentChar();return r===R||r===F?(y.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,c(),r===R&&(e.next(),_(e,"'")),n):(_(e,"'"),n)}(e)),d(e),n;if(!r&&!o&&!s)return n=f(t,13,function(e){d(e);let t="",n="";for(;t=A(e,Y);)n+=t;return n}(e)),y.INVALID_TOKEN_IN_PLACEHOLDER,c(),n.value,d(e),n;break}}return n}function $(e,t){const{currentType:n}=t;let r=null;const o=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||o!==R&&o!==D||(y.INVALID_LINKED_FORMAT,c()),o){case"@":return e.next(),r=f(t,8,"@"),t.inLinked=!0,r;case".":return d(e),e.next(),f(t,9,".");case":":return d(e),e.next(),f(t,10,":");default:return T(e)?(r=f(t,1,K(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(8!==n)return!1;m(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;m(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(d(e),$(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;m(e);const r=p(e.currentPeek());return e.resetPeek(),r}(e,t)?(d(e),f(t,12,function(e){let t="",n="";for(;t=k(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?p(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||t===D||!t)&&(t===R?(e.peek(),r()):L(e,!1))},o=r();return e.resetPeek(),o}(e,t)?(d(e),"{"===o?G(e,t)||r:f(t,11,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"%"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===D?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(8===n&&(y.INVALID_LINKED_FORMAT,c()),t.braceNest=0,t.inLinked=!1,V(e,t))}}function V(e,t){let n={type:14};if(t.braceNest>0)return G(e,t)||E(t);if(t.inLinked)return $(e,t)||E(t);switch(e.currentChar()){case"{":return G(e,t)||E(t);case"}":return y.UNBALANCED_CLOSING_BRACE,c(),e.next(),f(t,3,"}");case"@":return $(e,t)||E(t);default:{if(T(e))return n=f(t,1,K(e)),t.braceNest=0,t.inLinked=!1,n;const{isModulo:r,hasSpace:o}=function(e){const t=m(e),n="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:n,hasSpace:t.length>0}}(e);if(r)return o?f(t,0,U(e)):f(t,4,function(e){d(e);const t=e.currentChar();return"%"!==t&&(y.EXPECTED_TOKEN,c()),e.next(),"%"}(e));if(L(e))return f(t,0,U(e));break}}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:s}=l;return l.lastType=e,l.lastOffset=t,l.lastStartLoc=n,l.lastEndLoc=s,l.offset=o(),l.startLoc=c(),r.currentChar()===F?f(l,14):V(r,l)},currentOffset:o,currentPosition:c,context:i}}const X=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function Y(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function K(e={}){const t=!1!==e.location,{onError:n,onWarn:r}=e;function o(e,n,r){const o={type:e};return t&&(o.start=n,o.end=n,o.loc={start:r,end:r}),o}function c(e,n,r,o){o&&(e.type=o),t&&(e.end=n,e.loc&&(e.loc.end=r))}function s(e,t){const n=e.context(),r=o(3,n.offset,n.startLoc);return r.value=t,c(r,e.currentOffset(),e.currentPosition()),r}function a(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:s}=n,a=o(5,r,s);return a.index=parseInt(t,10),e.nextToken(),c(a,e.currentOffset(),e.currentPosition()),a}function i(e,t,n){const r=e.context(),{lastOffset:s,lastStartLoc:a}=r,l=o(4,s,a);return l.key=t,!0===n&&(l.modulo=!0),e.nextToken(),c(l,e.currentOffset(),e.currentPosition()),l}function u(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:s}=n,a=o(9,r,s);return a.value=t.replace(X,Y),e.nextToken(),c(a,e.currentOffset(),e.currentPosition()),a}function f(e){const t=e.context(),n=o(6,t.offset,t.startLoc);let r=e.nextToken();if(9===r.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:r,lastStartLoc:s}=n,a=o(8,r,s);return 12!==t.type?(y.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,a.value="",c(a,r,s),{nextConsumeToken:t,node:a}):(null==t.value&&(y.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,G(t)),a.value=t.value||"",c(a,e.currentOffset(),e.currentPosition()),{node:a})}(e);n.modifier=t.node,r=t.nextConsumeToken||e.nextToken()}switch(10!==r.type&&(y.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),r=e.nextToken(),2===r.type&&(r=e.nextToken()),r.type){case 11:null==r.value&&(y.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.key=function(e,t){const n=e.context(),r=o(7,n.offset,n.startLoc);return r.value=t,c(r,e.currentOffset(),e.currentPosition()),r}(e,r.value||"");break;case 5:null==r.value&&(y.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.key=i(e,r.value||"");break;case 6:null==r.value&&(y.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.key=a(e,r.value||"");break;case 7:null==r.value&&(y.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.key=u(e,r.value||"");break;default:{y.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const s=e.context(),a=o(7,s.offset,s.startLoc);return a.value="",c(a,s.offset,s.startLoc),n.key=a,c(n,s.offset,s.startLoc),{nextConsumeToken:r,node:n}}}return c(n,e.currentOffset(),e.currentPosition()),{node:n}}function E(e){const t=e.context(),n=o(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let r=null,l=null;do{const o=r||e.nextToken();switch(r=null,o.type){case 0:null==o.value&&(y.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(o)),n.items.push(s(e,o.value||""));break;case 6:null==o.value&&(y.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(o)),n.items.push(a(e,o.value||""));break;case 4:l=!0;break;case 5:null==o.value&&(y.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(o)),n.items.push(i(e,o.value||"",!!l)),l&&(g.USE_MODULO_SYNTAX,t.lastStartLoc,G(o),l=null);break;case 7:null==o.value&&(y.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(o)),n.items.push(u(e,o.value||""));break;case 8:{const t=f(e);n.items.push(t.node),r=t.nextConsumeToken||null;break}}}while(14!==t.currentType&&1!==t.currentType);return c(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function _(e){const t=e.context(),{offset:n,startLoc:r}=t,s=E(e);return 14===t.currentType?s:function(e,t,n,r){const s=e.context();let a=0===r.items.length;const l=o(1,t,n);l.cases=[],l.cases.push(r);do{const t=E(e);a||(a=0===t.items.length),l.cases.push(t)}while(14!==s.currentType);return a&&y.MUST_HAVE_MESSAGES_IN_PLURAL,c(l,e.currentOffset(),e.currentPosition()),l}(e,n,r,s)}return{parse:function(n){const r=W(n,l({},e)),s=r.context(),a=o(0,s.offset,s.startLoc);return t&&a.loc&&(a.loc.source=n),a.body=_(r),e.onCacheKey&&(a.cacheKey=e.onCacheKey(n)),14!==s.currentType&&(y.UNEXPECTED_LEXICAL_ANALYSIS,s.lastStartLoc,n[s.offset]),c(a,r.currentOffset(),r.currentPosition()),a}}}function G(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function $(e,t){for(let n=0;n<e.length;n++)V(e[n],t)}function V(e,t){switch(e.type){case 1:$(e.cases,t),t.helper("plural");break;case 2:$(e.items,t);break;case 6:V(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function H(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&V(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function B(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=h(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function j(e){switch(e.t=e.type,e.type){case 0:{const t=e;j(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)j(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)j(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;j(t.key),t.k=t.key,delete t.key,t.modifier&&(j(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function z(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?z(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const o=t.cases.length;for(let n=0;n<o&&(z(e,t.cases[n]),n!==o-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const o=t.items.length;for(let c=0;c<o&&(z(e,t.items[c]),c!==o-1);c++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),z(e,t.key),t.modifier?(e.push(", "),z(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}const Q=(e,t={})=>{const n=p(t.mode)?t.mode:"normal",r=p(t.filename)?t.filename:"message.intl",o=!!t.sourceMap,c=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",s=t.needIndent?t.needIndent:"arrow"!==n,a=e.helpers||[],l=function(e,t){const{sourceMap:n,filename:r,breakLineCode:o,needIndent:c}=t,s=!1!==t.location,a={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:o,needIndent:c,indentLevel:0};function l(e,t){a.code+=e}function i(e,t=!0){const n=t?o:"";l(c?n+"  ".repeat(e):n)}return s&&e.loc&&(a.source=e.loc.source),{context:()=>a,push:l,indent:function(e=!0){const t=++a.indentLevel;e&&i(t)},deindent:function(e=!0){const t=--a.indentLevel;e&&i(t)},newline:function(){i(a.indentLevel)},helper:e=>`_${e}`,needIndent:()=>a.needIndent}}(e,{mode:n,filename:r,sourceMap:o,breakLineCode:c,needIndent:s});l.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),l.indent(s),a.length>0&&(l.push(`const { ${h(a.map((e=>`${e}: _${e}`)),", ")} } = ctx`),l.newline()),l.push("return "),z(l,e),l.deindent(s),l.push("}"),delete e.helpers;const{code:i,map:u}=l.context();return{ast:e,code:i,map:u?u.toJSON():void 0}};function J(e,t={}){const n=l({},t),r=!!n.jit,o=!!n.minify,c=null==n.optimize||n.optimize,s=K(n).parse(e);return r?(c&&function(e){const t=e.body;2===t.type?B(t):t.cases.forEach((e=>B(e)))}(s),o&&j(s),{ast:s,code:""}):(H(s,n),Q(s,n))}function Z(e){return T(e)&&0===oe(e)&&(_(e,"b")||_(e,"body"))}const q=["b","body"];const ee=["c","cases"];const te=["s","static"];const ne=["i","items"];const re=["t","type"];function oe(e){return ie(e,re)}const ce=["v","value"];function se(e,t){const n=ie(e,ce);if(null!=n)return n;throw fe(t)}const ae=["m","modifier"];const le=["k","key"];function ie(e,t,n){for(let r=0;r<t.length;r++){const n=t[r];if(_(e,n)&&null!=e[n])return e[n]}return n}const ue=[...q,...ee,...te,...ne,...le,...ae,...ce,...re];function fe(e){return new Error(`unhandled node type: ${e}`)}const Ee=[];Ee[0]={w:[0],i:[3,0],"[":[4],o:[7]},Ee[1]={w:[1],".":[2],"[":[4],o:[7]},Ee[2]={w:[2],i:[3,0],0:[3,0]},Ee[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},Ee[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},Ee[5]={"'":[4,0],o:8,l:[5,0]},Ee[6]={'"':[4,0],o:8,l:[6,0]};const _e=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function me(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function de(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,_e.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}function pe(e){const t=[];let n,r,o,c,s,a,l,i=-1,u=0,f=0;const E=[];function _(){const t=e[i+1];if(5===u&&"'"===t||6===u&&'"'===t)return i++,o="\\"+t,E[0](),!0}for(E[0]=()=>{void 0===r?r=o:r+=o},E[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},E[2]=()=>{E[0](),f++},E[3]=()=>{if(f>0)f--,u=4,E[0]();else{if(f=0,void 0===r)return!1;if(r=de(r),!1===r)return!1;E[1]()}};null!==u;)if(i++,n=e[i],"\\"!==n||!_()){if(c=me(n),l=Ee[u],s=l[c]||l.l||8,8===s)return;if(u=s[0],void 0!==s[1]&&(a=E[s[1]],a&&(o=n,!1===a())))return;if(7===u)return t}}const Ne=new Map;function Te(e,t){return T(e)?e[t]:null}const Le=e=>e,Ae=e=>"",Ce="text",ke=e=>0===e.length?"":h(e),he=e=>null==e?"":m(e)||k(e)&&e.toString===A?JSON.stringify(e,null,2):String(e);function Oe(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function Ie(e={}){const t=e.locale,n=function(e){const t=o(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(o(e.named.count)||o(e.named.n))?o(e.named.count)?e.named.count:o(e.named.n)?e.named.n:t:t}(e),r=T(e.pluralRules)&&p(t)&&d(e.pluralRules[t])?e.pluralRules[t]:Oe,c=T(e.pluralRules)&&p(t)&&d(e.pluralRules[t])?Oe:void 0,s=e.list||[],a=e.named||u();o(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,a);function i(t){const n=d(e.messages)?e.messages(t):!!T(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):Ae)}const f=k(e.processor)&&d(e.processor.normalize)?e.processor.normalize:ke,E=k(e.processor)&&d(e.processor.interpolate)?e.processor.interpolate:he,_={list:e=>s[e],named:e=>a[e],plural:e=>e[r(n,e.length,c)],linked:(t,...n)=>{const[r,o]=n;let c="text",s="";1===n.length?T(r)?(s=r.modifier||s,c=r.type||c):p(r)&&(s=r||s):2===n.length&&(p(r)&&(s=r||s),p(o)&&(c=o||c));const a=i(t)(_),l="vnode"===c&&m(a)&&s?a[0]:a;return s?(u=s,e.modifiers?e.modifiers[u]:Le)(l,c):l;var u},message:i,type:k(e.processor)&&p(e.processor.type)?e.processor.type:Ce,interpolate:E,normalize:f,values:l(u(),s,a)};return _}let Se=null;const ge=ye("function:translate");function ye(e){return t=>Se&&Se.emit(e,t)}const be=g.__EXTEND_POINT__,Pe=O(be),De={NOT_FOUND_KEY:be,FALLBACK_TO_TRANSLATE:Pe(),CANNOT_FORMAT_NUMBER:Pe(),FALLBACK_TO_NUMBER_FORMAT:Pe(),CANNOT_FORMAT_DATE:Pe(),FALLBACK_TO_DATE_FORMAT:Pe(),EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:Pe(),__EXTEND_POINT__:Pe()},Me={[De.NOT_FOUND_KEY]:"Not found '{key}' key in '{locale}' locale messages.",[De.FALLBACK_TO_TRANSLATE]:"Fall back to translate '{key}' key with '{target}' locale.",[De.CANNOT_FORMAT_NUMBER]:"Cannot format a number value due to not supported Intl.NumberFormat.",[De.FALLBACK_TO_NUMBER_FORMAT]:"Fall back to number format '{key}' key with '{target}' locale.",[De.CANNOT_FORMAT_DATE]:"Cannot format a date value due to not supported Intl.DateTimeFormat.",[De.FALLBACK_TO_DATE_FORMAT]:"Fall back to datetime format '{key}' key with '{target}' locale.",[De.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER]:"This project is using Custom Message Compiler, which is an experimental feature. It may receive breaking changes or be removed in the future."};const Re=y.__EXTEND_POINT__,Ue=O(Re),ve={INVALID_ARGUMENT:Re,INVALID_DATE_ARGUMENT:Ue(),INVALID_ISO_DATE_ARGUMENT:Ue(),NOT_SUPPORT_NON_STRING_MESSAGE:Ue(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:Ue(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:Ue(),NOT_SUPPORT_LOCALE_TYPE:Ue(),__EXTEND_POINT__:Ue()};function xe(e,t){return null!=t.locale?we(t.locale):we(e.locale)}let Fe;function we(e){if(p(e))return e;if(d(e)){if(e.resolvedOnce&&null!=Fe)return Fe;if("Function"===e.constructor.name){const t=e();if(L(t))throw Error(ve.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return Fe=t}throw Error(ve.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(ve.NOT_SUPPORT_LOCALE_TYPE)}function We(e,t,n){return[...new Set([n,...m(t)?t:T(t)?Object.keys(t):p(t)?[t]:[n]])]}function Xe(e,t,n){let r=!0;for(let o=0;o<t.length&&N(r);o++){const c=t[o];p(c)&&(r=Ye(e,t[o],n))}return r}function Ye(e,t,n){let r;const o=t.split("-");do{r=Ke(e,o.join("-"),n),o.splice(-1,1)}while(o.length&&!0===r);return r}function Ke(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const o=t.replace(/!/g,"");e.push(o),(m(n)||k(n))&&n[o]&&(r=n[o])}return r}ve.INVALID_ARGUMENT,ve.INVALID_DATE_ARGUMENT,ve.INVALID_ISO_DATE_ARGUMENT,ve.NOT_SUPPORT_NON_STRING_MESSAGE,ve.NOT_SUPPORT_LOCALE_PROMISE_VALUE,ve.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION,ve.NOT_SUPPORT_LOCALE_TYPE;const Ge="9.14.4",$e="en-US",Ve=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let He,Be,je;let ze=null;let Qe=null;let Je=0;const Ze=e=>({[e]:u()});function qe(e,t,n,r,o){const{missing:c,onWarn:s}=e;if(null!==c){const r=c(e,n,t,o);return p(r)?r:t}return t}function et(e,t){return e!==t&&e.split("-")[0]===t.split("-")[0]}function tt(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let r=n+1;r<t.length;r++)if(et(e,t[r]))return!0;return!1}function nt(e){return t=>function(e,t){const n=(r=t,ie(r,q));var r;if(null==n)throw fe(0);if(1===oe(n)){const t=function(e){return ie(e,ee,[])}(n);return e.plural(t.reduce(((t,n)=>[...t,rt(e,n)]),[]))}return rt(e,n)}(t,e)}function rt(e,t){const n=function(e){return ie(e,te)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return ie(e,ne,[])}(t).reduce(((t,n)=>[...t,ot(e,n)]),[]);return e.normalize(n)}}function ot(e,t){const n=oe(t);switch(n){case 3:case 9:case 7:case 8:return se(t,n);case 4:{const r=t;if(_(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(_(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw fe(n)}case 5:{const r=t;if(_(r,"i")&&o(r.i))return e.interpolate(e.list(r.i));if(_(r,"index")&&o(r.index))return e.interpolate(e.list(r.index));throw fe(n)}case 6:{const n=t,r=function(e){return ie(e,ae)}(n),o=function(e){const t=ie(e,le);if(t)return t;throw fe(6)}(n);return e.linked(ot(e,o),r?ot(e,r):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}const ct=e=>e;let st=u();function at(e,t={}){let n=!1;const r=t.onError||P;return t.onError=e=>{n=!0,r(e)},{...J(e,t),detectError:n}}const lt=()=>"",it=e=>d(e);function ut(e,t,n,r,o,c){const{messages:s,onWarn:a,messageResolver:l,localeFallbacker:i}=e,f=i(e,r,n);let E,_=u(),m=null;for(let d=0;d<f.length&&(E=f[d],_=s[E]||u(),null===(m=l(_,t))&&(m=_[t]),!(p(m)||Z(m)||it(m)));d++)if(!tt(E,f)){const n=qe(e,t,E,0,"translate");n!==t&&(m=n)}return[m,E,_]}function ft(e,t,r,o,c,s){const{messageCompiler:a,warnHtmlMessage:l}=e;if(it(o)){const e=o;return e.locale=e.locale||r,e.key=e.key||t,e}if(null==a){const e=()=>o;return e.locale=r,e.key=t,e}const i=a(o,function(e,t,r,o,c,s){return{locale:t,key:r,warnHtmlMessage:c,onError:e=>{throw s&&s(e),e},onCacheKey:e=>n(t,r,e)}}(0,r,c,0,l,s));return i.locale=r,i.key=t,i.source=o,i}function Et(...e){const[t,n,r]=e,c=u();if(!(p(t)||o(t)||it(t)||Z(t)))throw Error(ve.INVALID_ARGUMENT);const s=o(t)?String(t):(it(t),t);return o(n)?c.plural=n:p(n)?c.default=n:k(n)&&!a(n)?c.named=n:m(n)&&(c.list=n),o(r)?c.plural=r:p(r)?c.default=r:k(r)&&l(c,r),[s,c]}const _t=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function mt(...e){const[t,n,r,s]=e,a=u();let l,i=u();if(p(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(ve.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();l=new Date(n);try{l.toISOString()}catch(f){throw Error(ve.INVALID_ISO_DATE_ARGUMENT)}}else if(c(t)){if(isNaN(t.getTime()))throw Error(ve.INVALID_DATE_ARGUMENT);l=t}else{if(!o(t))throw Error(ve.INVALID_ARGUMENT);l=t}return p(n)?a.key=n:k(n)&&Object.keys(n).forEach((e=>{_t.includes(e)?i[e]=n[e]:a[e]=n[e]})),p(r)?a.locale=r:k(r)&&(i=r),k(s)&&(i=s),[a.key||"",l,a,i]}const dt=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function pt(...e){const[t,n,r,c]=e,s=u();let a=u();if(!o(t))throw Error(ve.INVALID_ARGUMENT);const l=t;return p(n)?s.key=n:k(n)&&Object.keys(n).forEach((e=>{dt.includes(e)?a[e]=n[e]:s[e]=n[e]})),p(r)?s.locale=r:k(r)&&(a=r),k(c)&&(a=c),[s.key||"",l,s,a]}return e.AST_NODE_PROPS_KEYS=ue,e.CompileErrorCodes=y,e.CoreErrorCodes=ve,e.CoreWarnCodes=De,e.DATETIME_FORMAT_OPTIONS_KEYS=_t,e.DEFAULT_LOCALE=$e,e.DEFAULT_MESSAGE_DATA_TYPE=Ce,e.MISSING_RESOLVE_VALUE="",e.NOT_REOSLVED=-1,e.NUMBER_FORMAT_OPTIONS_KEYS=dt,e.VERSION=Ge,e.clearCompileCache=function(){st=u()},e.clearDateTimeFormat=function(e,t,n){const r=e;for(const o in n){const e=`${t}__${o}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}},e.clearNumberFormat=function(e,t,n){const r=e;for(const o in n){const e=`${t}__${o}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}},e.compile=function(e,t){if(p(e)){!N(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||ct)(e),r=st[n];if(r)return r;const{ast:o,detectError:c}=at(e,{...t,location:!1,jit:!0}),s=nt(o);return c?s:st[n]=s}{const t=e.cacheKey;if(t){const n=st[t];return n||(st[t]=nt(e))}return nt(e)}},e.compileToFunction=(e,t)=>{if(!p(e))throw Error(ve.NOT_SUPPORT_NON_STRING_MESSAGE);{!N(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||ct)(e),r=st[n];if(r)return r;const{code:o,detectError:c}=at(e,t),s=new Function(`return ${o}`)();return c?s:st[n]=s}},e.createCompileError=b,e.createCoreContext=function(e={}){const t=d(e.onWarn)?e.onWarn:I,n=p(e.version)?e.version:Ge,r=p(e.locale)||d(e.locale)?e.locale:$e,o=d(r)?$e:r,c=m(e.fallbackLocale)||k(e.fallbackLocale)||p(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:o,a=k(e.messages)?e.messages:Ze(o),i=k(e.datetimeFormats)?e.datetimeFormats:Ze(o),f=k(e.numberFormats)?e.numberFormats:Ze(o),E=l(u(),e.modifiers,{upper:(e,t)=>"text"===t&&p(e)?e.toUpperCase():"vnode"===t&&T(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&p(e)?e.toLowerCase():"vnode"===t&&T(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&p(e)?Ve(e):"vnode"===t&&T(e)&&"__v_isVNode"in e?Ve(e.children):e}),_=e.pluralRules||u(),L=d(e.missing)?e.missing:null,A=!N(e.missingWarn)&&!s(e.missingWarn)||e.missingWarn,C=!N(e.fallbackWarn)&&!s(e.fallbackWarn)||e.fallbackWarn,h=!!e.fallbackFormat,O=!!e.unresolving,S=d(e.postTranslation)?e.postTranslation:null,g=k(e.processor)?e.processor:null,y=!N(e.warnHtmlMessage)||e.warnHtmlMessage,b=!!e.escapeParameter,P=d(e.messageCompiler)?e.messageCompiler:He,D=d(e.messageResolver)?e.messageResolver:Be||Te,M=d(e.localeFallbacker)?e.localeFallbacker:je||We,R=T(e.fallbackContext)?e.fallbackContext:void 0,U=e,v=T(U.__datetimeFormatters)?U.__datetimeFormatters:new Map,x=T(U.__numberFormatters)?U.__numberFormatters:new Map,F=T(U.__meta)?U.__meta:{};Je++;const w={version:n,cid:Je,locale:r,fallbackLocale:c,messages:a,modifiers:E,pluralRules:_,missing:L,missingWarn:A,fallbackWarn:C,fallbackFormat:h,unresolving:O,postTranslation:S,processor:g,warnHtmlMessage:y,escapeParameter:b,messageCompiler:P,messageResolver:D,localeFallbacker:M,fallbackContext:R,onWarn:t,__meta:F};return w.datetimeFormats=i,w.numberFormats=f,w.__datetimeFormatters=v,w.__numberFormatters=x,w},e.createCoreError=function(e){return b(e,null,void 0)},e.createMessageContext=Ie,e.datetime=function(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:o,onWarn:c,localeFallbacker:s}=e,{__datetimeFormatters:i}=e,[u,f,E,_]=mt(...t);N(E.missingWarn)?E.missingWarn:e.missingWarn,N(E.fallbackWarn)?E.fallbackWarn:e.fallbackWarn;const m=!!E.part,d=xe(e,E),T=s(e,o,d);if(!p(u)||""===u)return new Intl.DateTimeFormat(d,_).format(f);let L,A={},C=null;for(let a=0;a<T.length&&(L=T[a],A=n[L]||{},C=A[u],!k(C));a++)qe(e,u,L,0,"datetime format");if(!k(C)||!p(L))return r?-1:u;let h=`${L}__${u}`;a(_)||(h=`${h}__${JSON.stringify(_)}`);let O=i.get(h);return O||(O=new Intl.DateTimeFormat(L,l({},C,_)),i.set(h,O)),m?O.formatToParts(f):O.format(f)},e.fallbackWithLocaleChain=function(e,t,n){const r=p(n)?n:$e,o=e;o.__localeChainCache||(o.__localeChainCache=new Map);let c=o.__localeChainCache.get(r);if(!c){c=[];let e=[n];for(;m(e);)e=Xe(c,e,t);const s=m(t)||!k(t)?t:t.default?t.default:null;e=p(s)?[s]:s,m(e)&&Xe(c,e,!1),o.__localeChainCache.set(r,c)}return c},e.fallbackWithSimple=We,e.getAdditionalMeta=()=>ze,e.getDevToolsHook=function(){return Se},e.getFallbackContext=()=>Qe,e.getLocale=xe,e.getWarnMessage=function(e,...n){return function(e,...n){return 1===n.length&&T(n[0])&&(n=n[0]),n&&n.hasOwnProperty||(n={}),e.replace(t,((e,t)=>n.hasOwnProperty(t)?n[t]:""))}(Me[e],...n)},e.handleMissing=qe,e.initI18nDevTools=function(e,t,n){Se&&Se.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})},e.isAlmostSameLocale=et,e.isImplicitFallback=tt,e.isMessageAST=Z,e.isMessageFunction=it,e.isTranslateFallbackWarn=function(e,t){return e instanceof RegExp?e.test(t):e},e.isTranslateMissingWarn=function(e,t){return e instanceof RegExp?e.test(t):e},e.number=function(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:o,onWarn:c,localeFallbacker:s}=e,{__numberFormatters:i}=e,[u,f,E,_]=pt(...t);N(E.missingWarn)?E.missingWarn:e.missingWarn,N(E.fallbackWarn)?E.fallbackWarn:e.fallbackWarn;const m=!!E.part,d=xe(e,E),T=s(e,o,d);if(!p(u)||""===u)return new Intl.NumberFormat(d,_).format(f);let L,A={},C=null;for(let a=0;a<T.length&&(L=T[a],A=n[L]||{},C=A[u],!k(C));a++)qe(e,u,L,0,"number format");if(!k(C)||!p(L))return r?-1:u;let h=`${L}__${u}`;a(_)||(h=`${h}__${JSON.stringify(_)}`);let O=i.get(h);return O||(O=new Intl.NumberFormat(L,l({},C,_)),i.set(h,O)),m?O.formatToParts(f):O.format(f)},e.parse=pe,e.parseDateTimeArgs=mt,e.parseNumberArgs=pt,e.parseTranslateArgs=Et,e.registerLocaleFallbacker=function(e){je=e},e.registerMessageCompiler=function(e){He=e},e.registerMessageResolver=function(e){Be=e},e.resolveLocale=we,e.resolveValue=function(e,t){if(!T(e))return null;let n=Ne.get(t);if(n||(n=pe(t),n&&Ne.set(t,n)),!n)return null;const r=n.length;let o=e,c=0;for(;c<r;){const e=n[c];if(ue.includes(e)&&Z(o))return null;const t=o[e];if(void 0===t)return null;if(d(o))return null;o=t,c++}return o},e.resolveWithKeyValue=Te,e.setAdditionalMeta=e=>{ze=e},e.setDevToolsHook=function(e){Se=e},e.setFallbackContext=e=>{Qe=e},e.translate=function(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:c,messageCompiler:s,fallbackLocale:a,messages:l}=e,[i,E]=Et(...t),_=N(E.missingWarn)?E.missingWarn:e.missingWarn,d=N(E.fallbackWarn)?E.fallbackWarn:e.fallbackWarn,L=N(E.escapeParameter)?E.escapeParameter:e.escapeParameter,A=!!E.resolvedMessage,C=p(E.default)||N(E.default)?N(E.default)?s?i:()=>i:E.default:n?s?i:()=>i:"",k=n||""!==C,h=xe(e,E);L&&function(e){m(e.list)?e.list=e.list.map((e=>p(e)?f(e):e)):T(e.named)&&Object.keys(e.named).forEach((t=>{p(e.named[t])&&(e.named[t]=f(e.named[t]))}))}(E);let[O,I,S]=A?[i,h,l[h]||u()]:ut(e,i,h,a,d,_),g=O,y=i;if(A||p(g)||Z(g)||it(g)||k&&(g=C,y=g),!(A||(p(g)||Z(g)||it(g))&&p(I)))return c?-1:i;let b=!1;const P=it(g)?g:ft(e,i,I,g,y,(()=>{b=!0}));if(b)return g;const D=function(e,t,n,r){const{modifiers:c,pluralRules:s,messageResolver:a,fallbackLocale:l,fallbackWarn:i,missingWarn:u,fallbackContext:f}=e,E=r=>{let o=a(n,r);if(null==o&&f){const[,,e]=ut(f,r,t,l,i,u);o=a(e,r)}if(p(o)||Z(o)){let n=!1;const c=ft(e,r,t,o,r,(()=>{n=!0}));return n?lt:c}return it(o)?o:lt},_={locale:t,modifiers:c,pluralRules:s,messages:E};e.processor&&(_.processor=e.processor);r.list&&(_.list=r.list);r.named&&(_.named=r.named);o(r.plural)&&(_.pluralIndex=r.plural);return _}(e,I,S,E),M=function(e,t,n){const r=t(n);return r}(0,P,Ie(D));return r?r(M,i):M},e.translateDevTools=ge,e.updateFallbackLocale=function(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)},e}({});
