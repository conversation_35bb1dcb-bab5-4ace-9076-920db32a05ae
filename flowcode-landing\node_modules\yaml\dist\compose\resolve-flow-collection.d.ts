import { YAMLMap } from '../nodes/YAMLMap';
import { YAMLSeq } from '../nodes/YAMLSeq';
import type { FlowCollection } from '../parse/cst';
import type { CollectionTag } from '../schema/types';
import type { ComposeContext, ComposeNode } from './compose-node';
import type { ComposeErrorHandler } from './composer';
export declare function resolveFlowCollection({ composeNode, composeEmptyNode }: ComposeNode, ctx: ComposeContext, fc: FlowCollection, onError: ComposeErrorHandler, tag?: CollectionTag): YAMLMap.Parsed<import('../index').ParsedNode, import('../index').ParsedNode | null> | YAMLSeq.Parsed<import('../index').ParsedNode>;
