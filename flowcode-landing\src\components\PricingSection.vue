<template>
  <section id="pricing" class="section-padding bg-white">
    <div class="max-w-7xl mx-auto">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          {{ $t('pricing.title') }}
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          {{ $t('pricing.subtitle') }}
        </p>
      </div>

      <!-- Pricing Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <!-- Free Plan -->
        <div class="card relative">
          <div class="text-center">
            <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('pricing.plans.free.name') }}</h3>
            <p class="text-gray-600 mb-6">{{ $t('pricing.plans.free.description') }}</p>
            <div class="mb-6">
              <span class="text-4xl font-bold text-gray-900">{{ $t('pricing.plans.free.price') }}</span>
              <span class="text-gray-600">{{ $t('pricing.plans.free.period') }}</span>
            </div>
            <button class="btn-secondary w-full mb-6">
              {{ $t('pricing.plans.free.button') }}
            </button>
          </div>
          
          <div class="space-y-4">
            <div v-for="feature in $t('pricing.plans.free.features')" :key="feature" class="flex items-center">
              <CheckIcon class="w-5 h-5 text-green-500 mr-3" />
              <span class="text-gray-600">{{ feature }}</span>
            </div>
            <div v-for="feature in $t('pricing.plans.free.unavailable')" :key="feature" class="flex items-center">
              <XIcon class="w-5 h-5 text-gray-400 mr-3" />
              <span class="text-gray-400">{{ feature }}</span>
            </div>
          </div>
        </div>

        <!-- Pro Plan -->
        <div class="card relative border-2 border-primary-500 transform scale-105">
          <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <span class="bg-primary-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
              {{ $t('pricing.plans.pro.popular') }}
            </span>
          </div>

          <div class="text-center">
            <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('pricing.plans.pro.name') }}</h3>
            <p class="text-gray-600 mb-6">{{ $t('pricing.plans.pro.description') }}</p>
            <div class="mb-6">
              <span class="text-4xl font-bold text-primary-600">{{ $t('pricing.plans.pro.price') }}</span>
              <span class="text-gray-600">{{ $t('pricing.plans.pro.period') }}</span>
            </div>
            <button class="btn-primary w-full mb-6">
              {{ $t('pricing.plans.pro.button') }}
            </button>
          </div>

          <div class="space-y-4">
            <div v-for="feature in $t('pricing.plans.pro.features')" :key="feature" class="flex items-center">
              <CheckIcon class="w-5 h-5 text-green-500 mr-3" />
              <span class="text-gray-600">{{ feature }}</span>
            </div>
          </div>
        </div>

        <!-- Enterprise Plan -->
        <div class="card relative">
          <div class="text-center">
            <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('pricing.plans.enterprise.name') }}</h3>
            <p class="text-gray-600 mb-6">{{ $t('pricing.plans.enterprise.description') }}</p>
            <div class="mb-6">
              <span class="text-4xl font-bold text-gray-900">{{ $t('pricing.plans.enterprise.price') }}</span>
              <span class="text-gray-600">{{ $t('pricing.plans.enterprise.period') }}</span>
            </div>
            <button class="btn-secondary w-full mb-6">
              {{ $t('pricing.plans.enterprise.button') }}
            </button>
          </div>

          <div class="space-y-4">
            <div v-for="feature in $t('pricing.plans.enterprise.features')" :key="feature" class="flex items-center">
              <CheckIcon class="w-5 h-5 text-green-500 mr-3" />
              <span class="text-gray-600">{{ feature }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- FAQ Section -->
      <div class="mt-16">
        <h3 class="text-2xl font-bold text-gray-900 text-center mb-8">{{ $t('pricing.faq.title') }}</h3>
        <div class="max-w-4xl mx-auto space-y-6">
          <div v-for="faq in $t('pricing.faq.questions')" :key="faq.question" class="card">
            <h4 class="font-semibold text-gray-900 mb-2">{{ faq.question }}</h4>
            <p class="text-gray-600">{{ faq.answer }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { Check, X } from 'lucide-vue-next'

const CheckIcon = Check
const XIcon = X
</script>
