/*!
  * message-compiler v9.14.4
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var IntlifyMessageCompiler=function(e){"use strict";function t(e,t,n){return{line:e,column:t,offset:n}}function n(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const r=Object.assign,c=e=>"string"==typeof e;function o(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}const s={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2},u={[s.USE_MODULO_SYNTAX]:"Use modulo before '{{0}}'."};function a(e,t,...n){const r={message:String(e),code:e};return t&&(r.location=t),r}const i={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17},l={[i.EXPECTED_TOKEN]:"Expected token: '{0}'",[i.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[i.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[i.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[i.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[i.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[i.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[i.EMPTY_PLACEHOLDER]:"Empty placeholder",[i.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[i.INVALID_LINKED_FORMAT]:"Invalid linked format",[i.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[i.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[i.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[i.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[i.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[i.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function E(e,t,n={}){const{domain:r,messages:c,args:o}=n,s=new SyntaxError(String(e));return s.code=e,t&&(s.location=t),s.domain=r,s}const f=/<\/?[\w\s="/.':;#-\/]+>/,d=" ",L="\r",N="\n",_=String.fromCharCode(8232),p=String.fromCharCode(8233);function C(e){const t=e;let n=0,r=1,c=1,o=0;const s=e=>t[e]===L&&t[e+1]===N,u=e=>t[e]===p,a=e=>t[e]===_,i=e=>s(e)||(e=>t[e]===N)(e)||u(e)||a(e),l=e=>s(e)||u(e)||a(e)?N:t[e];function E(){return o=0,i(n)&&(r++,c=0),s(n)&&n++,n++,c++,t[n]}return{index:()=>n,line:()=>r,column:()=>c,peekOffset:()=>o,charAt:l,currentChar:()=>l(n),currentPeek:()=>l(n+o),next:E,peek:function(){return s(n+o)&&o++,o++,t[n+o]},reset:function(){n=0,r=1,c=1,o=0},resetPeek:function(e=0){o=e},skipToPeek:function(){const e=n+o;for(;e!==n;)E();o=0}}}const T=void 0,k="'";function A(e,r={}){const c=!1!==r.location,o=C(e),s=()=>o.index(),u=()=>t(o.line(),o.column(),o.index()),a=u(),l=s(),E={currentType:14,offset:l,startLoc:a,endLoc:a,lastType:14,lastOffset:l,lastStartLoc:a,lastEndLoc:a,braceNest:0,inLinked:!1,text:""},f=()=>E,{onError:L}=r;function _(e,t,r){e.endLoc=u(),e.currentType=t;const o={type:t};return c&&(o.loc=n(e.startLoc,e.endLoc)),null!=r&&(o.value=r),o}const p=e=>_(e,14);function A(e,t){return e.currentChar()===t?(e.next(),t):(i.EXPECTED_TOKEN,u(),"")}function I(e){let t="";for(;e.currentPeek()===d||e.currentPeek()===N;)t+=e.currentPeek(),e.peek();return t}function h(e){const t=I(e);return e.skipToPeek(),t}function S(e){if(e===T)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function P(e,t){const{currentType:n}=t;if(2!==n)return!1;I(e);const r=function(e){if(e===T)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function O(e){I(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function D(e,t=!0){const n=(t=!1,r="",c=!1)=>{const o=e.currentPeek();return"{"===o?"%"!==r&&t:"@"!==o&&o?"%"===o?(e.peek(),n(t,"%",!0)):"|"===o?!("%"!==r&&!c)||!(r===d||r===N):o===d?(e.peek(),n(!0,d,c)):o!==N||(e.peek(),n(!0,N,c)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function y(e,t){const n=e.currentChar();return n===T?T:t(n)?(e.next(),n):null}function m(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function U(e){return y(e,m)}function b(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function x(e){return y(e,b)}function v(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function R(e){return y(e,v)}function M(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function g(e){return y(e,M)}function X(e){let t="",n="";for(;t=R(e);)n+=t;return n}function Y(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!D(e))break;t+=n,e.next()}else if(n===d||n===N)if(D(e))t+=n,e.next();else{if(O(e))break;t+=n,e.next()}else t+=n,e.next()}return t}function K(e){return e!==k&&e!==N}function w(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return H(e,t,4);case"U":return H(e,t,6);default:return i.UNKNOWN_ESCAPE_SEQUENCE,u(),""}}function H(e,t,n){A(e,t);let r="";for(let c=0;c<n;c++){const t=g(e);if(!t){i.INVALID_UNICODE_ESCAPE_SEQUENCE,u(),e.currentChar();break}r+=t}return`\\${t}${r}`}function G(e){return"{"!==e&&"}"!==e&&e!==d&&e!==N}function $(e){h(e);const t=A(e,"|");return h(e),t}function B(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(i.NOT_ALLOW_NEST_PLACEHOLDER,u()),e.next(),n=_(t,2,"{"),h(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&(i.EMPTY_PLACEHOLDER,u()),e.next(),n=_(t,3,"}"),t.braceNest--,t.braceNest>0&&h(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&(i.UNTERMINATED_CLOSING_BRACE,u()),n=V(e,t)||p(t),t.braceNest=0,n;default:{let r=!0,c=!0,o=!0;if(O(e))return t.braceNest>0&&(i.UNTERMINATED_CLOSING_BRACE,u()),n=_(t,1,$(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return i.UNTERMINATED_CLOSING_BRACE,u(),t.braceNest=0,F(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;I(e);const r=S(e.currentPeek());return e.resetPeek(),r}(e,t))return n=_(t,5,function(e){h(e);let t="",n="";for(;t=x(e);)n+=t;return e.currentChar()===T&&(i.UNTERMINATED_CLOSING_BRACE,u()),n}(e)),h(e),n;if(c=P(e,t))return n=_(t,6,function(e){h(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${X(e)}`):t+=X(e),e.currentChar()===T&&(i.UNTERMINATED_CLOSING_BRACE,u()),t}(e)),h(e),n;if(o=function(e,t){const{currentType:n}=t;if(2!==n)return!1;I(e);const r=e.currentPeek()===k;return e.resetPeek(),r}(e,t))return n=_(t,7,function(e){h(e),A(e,"'");let t="",n="";for(;t=y(e,K);)n+="\\"===t?w(e):t;const r=e.currentChar();return r===N||r===T?(i.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,u(),r===N&&(e.next(),A(e,"'")),n):(A(e,"'"),n)}(e)),h(e),n;if(!r&&!c&&!o)return n=_(t,13,function(e){h(e);let t="",n="";for(;t=y(e,G);)n+=t;return n}(e)),i.INVALID_TOKEN_IN_PLACEHOLDER,u(),n.value,h(e),n;break}}return n}function V(e,t){const{currentType:n}=t;let r=null;const c=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||c!==N&&c!==d||(i.INVALID_LINKED_FORMAT,u()),c){case"@":return e.next(),r=_(t,8,"@"),t.inLinked=!0,r;case".":return h(e),e.next(),_(t,9,".");case":":return h(e),e.next(),_(t,10,":");default:return O(e)?(r=_(t,1,$(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(8!==n)return!1;I(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;I(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(h(e),V(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;I(e);const r=S(e.currentPeek());return e.resetPeek(),r}(e,t)?(h(e),_(t,12,function(e){let t="",n="";for(;t=U(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?S(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||t===d||!t)&&(t===N?(e.peek(),r()):D(e,!1))},c=r();return e.resetPeek(),c}(e,t)?(h(e),"{"===c?B(e,t)||r:_(t,11,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"%"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===d?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(8===n&&(i.INVALID_LINKED_FORMAT,u()),t.braceNest=0,t.inLinked=!1,F(e,t))}}function F(e,t){let n={type:14};if(t.braceNest>0)return B(e,t)||p(t);if(t.inLinked)return V(e,t)||p(t);switch(e.currentChar()){case"{":return B(e,t)||p(t);case"}":return i.UNBALANCED_CLOSING_BRACE,u(),e.next(),_(t,3,"}");case"@":return V(e,t)||p(t);default:{if(O(e))return n=_(t,1,$(e)),t.braceNest=0,t.inLinked=!1,n;const{isModulo:r,hasSpace:c}=function(e){const t=I(e),n="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:n,hasSpace:t.length>0}}(e);if(r)return c?_(t,0,Y(e)):_(t,4,function(e){h(e);const t=e.currentChar();return"%"!==t&&(i.EXPECTED_TOKEN,u()),e.next(),"%"}(e));if(D(e))return _(t,0,Y(e));break}}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:r}=E;return E.lastType=e,E.lastOffset=t,E.lastStartLoc=n,E.lastEndLoc=r,E.offset=s(),E.startLoc=u(),o.currentChar()===T?_(E,14):F(o,E)},currentOffset:s,currentPosition:u,context:f}}const I="parser",h=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function S(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function P(e={}){const t=!1!==e.location,{onError:n,onWarn:c}=e;function o(e,n,r){const c={type:e};return t&&(c.start=n,c.end=n,c.loc={start:r,end:r}),c}function u(e,n,r,c){c&&(e.type=c),t&&(e.end=n,e.loc&&(e.loc.end=r))}function a(e,t){const n=e.context(),r=o(3,n.offset,n.startLoc);return r.value=t,u(r,e.currentOffset(),e.currentPosition()),r}function l(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:c}=n,s=o(5,r,c);return s.index=parseInt(t,10),e.nextToken(),u(s,e.currentOffset(),e.currentPosition()),s}function E(e,t,n){const r=e.context(),{lastOffset:c,lastStartLoc:s}=r,a=o(4,c,s);return a.key=t,!0===n&&(a.modulo=!0),e.nextToken(),u(a,e.currentOffset(),e.currentPosition()),a}function f(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:c}=n,s=o(9,r,c);return s.value=t.replace(h,S),e.nextToken(),u(s,e.currentOffset(),e.currentPosition()),s}function d(e){const t=e.context(),n=o(6,t.offset,t.startLoc);let r=e.nextToken();if(9===r.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:r,lastStartLoc:c}=n,s=o(8,r,c);return 12!==t.type?(i.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,s.value="",u(s,r,c),{nextConsumeToken:t,node:s}):(null==t.value&&(i.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,O(t)),s.value=t.value||"",u(s,e.currentOffset(),e.currentPosition()),{node:s})}(e);n.modifier=t.node,r=t.nextConsumeToken||e.nextToken()}switch(10!==r.type&&(i.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,O(r)),r=e.nextToken(),2===r.type&&(r=e.nextToken()),r.type){case 11:null==r.value&&(i.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,O(r)),n.key=function(e,t){const n=e.context(),r=o(7,n.offset,n.startLoc);return r.value=t,u(r,e.currentOffset(),e.currentPosition()),r}(e,r.value||"");break;case 5:null==r.value&&(i.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,O(r)),n.key=E(e,r.value||"");break;case 6:null==r.value&&(i.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,O(r)),n.key=l(e,r.value||"");break;case 7:null==r.value&&(i.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,O(r)),n.key=f(e,r.value||"");break;default:{i.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const c=e.context(),s=o(7,c.offset,c.startLoc);return s.value="",u(s,c.offset,c.startLoc),n.key=s,u(n,c.offset,c.startLoc),{nextConsumeToken:r,node:n}}}return u(n,e.currentOffset(),e.currentPosition()),{node:n}}function L(e){const t=e.context(),n=o(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let r=null,c=null;do{const o=r||e.nextToken();switch(r=null,o.type){case 0:null==o.value&&(i.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,O(o)),n.items.push(a(e,o.value||""));break;case 6:null==o.value&&(i.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,O(o)),n.items.push(l(e,o.value||""));break;case 4:c=!0;break;case 5:null==o.value&&(i.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,O(o)),n.items.push(E(e,o.value||"",!!c)),c&&(s.USE_MODULO_SYNTAX,t.lastStartLoc,O(o),c=null);break;case 7:null==o.value&&(i.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,O(o)),n.items.push(f(e,o.value||""));break;case 8:{const t=d(e);n.items.push(t.node),r=t.nextConsumeToken||null;break}}}while(14!==t.currentType&&1!==t.currentType);return u(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function N(e){const t=e.context(),{offset:n,startLoc:r}=t,c=L(e);return 14===t.currentType?c:function(e,t,n,r){const c=e.context();let s=0===r.items.length;const a=o(1,t,n);a.cases=[],a.cases.push(r);do{const t=L(e);s||(s=0===t.items.length),a.cases.push(t)}while(14!==c.currentType);return s&&i.MUST_HAVE_MESSAGES_IN_PLURAL,u(a,e.currentOffset(),e.currentPosition()),a}(e,n,r,c)}return{parse:function(n){const c=A(n,r({},e)),s=c.context(),a=o(0,s.offset,s.startLoc);return t&&a.loc&&(a.loc.source=n),a.body=N(c),e.onCacheKey&&(a.cacheKey=e.onCacheKey(n)),14!==s.currentType&&(i.UNEXPECTED_LEXICAL_ANALYSIS,s.lastStartLoc,n[s.offset]),u(a,c.currentOffset(),c.currentPosition()),a}}}function O(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function D(e,t){for(let n=0;n<e.length;n++)y(e[n],t)}function y(e,t){switch(e.type){case 1:D(e.cases,t),t.helper("plural");break;case 2:D(e.items,t);break;case 6:y(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function m(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&y(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function U(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=o(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function b(e){switch(e.t=e.type,e.type){case 0:{const t=e;b(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)b(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)b(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;b(t.key),t.k=t.key,delete t.key,t.modifier&&(b(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function x(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?x(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const c=t.cases.length;for(let n=0;n<c&&(x(e,t.cases[n]),n!==c-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const c=t.items.length;for(let o=0;o<c&&(x(e,t.items[o]),o!==c-1);o++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),x(e,t.key),t.modifier?(e.push(", "),x(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}return e.CompileErrorCodes=i,e.CompileWarnCodes=s,e.ERROR_DOMAIN=I,e.LOCATION_STUB={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}},e.baseCompile=function(e,t={}){const n=r({},t),s=!!n.jit,u=!!n.minify,a=null==n.optimize||n.optimize,i=P(n).parse(e);return s?(a&&function(e){const t=e.body;2===t.type?U(t):t.cases.forEach((e=>U(e)))}(i),u&&b(i),{ast:i,code:""}):(m(i,n),((e,t={})=>{const n=c(t.mode)?t.mode:"normal",r=c(t.filename)?t.filename:"message.intl",s=!!t.sourceMap,u=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",a=t.needIndent?t.needIndent:"arrow"!==n,i=e.helpers||[],l=function(e,t){const{sourceMap:n,filename:r,breakLineCode:c,needIndent:o}=t,s=!1!==t.location,u={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:c,needIndent:o,indentLevel:0};function a(e,t){u.code+=e}function i(e,t=!0){const n=t?c:"";a(o?n+"  ".repeat(e):n)}return s&&e.loc&&(u.source=e.loc.source),{context:()=>u,push:a,indent:function(e=!0){const t=++u.indentLevel;e&&i(t)},deindent:function(e=!0){const t=--u.indentLevel;e&&i(t)},newline:function(){i(u.indentLevel)},helper:e=>`_${e}`,needIndent:()=>u.needIndent}}(e,{mode:n,filename:r,sourceMap:s,breakLineCode:u,needIndent:a});l.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),l.indent(a),i.length>0&&(l.push(`const { ${o(i.map((e=>`${e}: _${e}`)),", ")} } = ctx`),l.newline()),l.push("return "),x(l,e),l.deindent(a),l.push("}"),delete e.helpers;const{code:E,map:f}=l.context();return{ast:e,code:E,map:f?f.toJSON():void 0}})(i,n))},e.createCompileError=E,e.createCompileWarn=a,e.createLocation=n,e.createParser=P,e.createPosition=t,e.defaultOnError=function(e){throw e},e.detectHtmlTag=e=>f.test(e),e.errorMessages=l,e.warnMessages=u,e}({});
