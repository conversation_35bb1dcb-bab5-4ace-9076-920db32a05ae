<template>
  <section class="section-padding gradient-bg relative overflow-hidden">
    <!-- Background decorations -->
    <div class="absolute inset-0">
      <div class="absolute top-0 left-0 w-96 h-96 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-slow"></div>
      <div class="absolute bottom-0 right-0 w-96 h-96 bg-accent-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-slow animation-delay-2000"></div>
    </div>

    <div class="relative max-w-4xl mx-auto text-center">
      <h2 class="text-3xl md:text-5xl font-bold text-gray-900 mb-6">
        准备好让AI改变你的
        <br />
        <span class="gradient-text">编程方式了吗？</span>
      </h2>
      
      <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
        加入10万+开发者的行列，体验下一代AI编程助手带来的效率革命。
        立即开始，让编程变得更加智能、高效、有趣！
      </p>

      <!-- CTA Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
        <button class="btn-primary text-lg px-8 py-4 group">
          <RocketIcon class="w-5 h-5 mr-2 group-hover:animate-bounce" />
          免费开始体验
        </button>
        <button class="btn-secondary text-lg px-8 py-4">
          <CalendarIcon class="w-5 h-5 mr-2" />
          预约演示
        </button>
      </div>

      <!-- Trust indicators -->
      <div class="flex flex-col sm:flex-row items-center justify-center gap-8 text-sm text-gray-500">
        <div class="flex items-center">
          <ShieldCheckIcon class="w-5 h-5 mr-2 text-green-500" />
          <span>14天免费试用</span>
        </div>
        <div class="flex items-center">
          <CreditCardIcon class="w-5 h-5 mr-2 text-green-500" />
          <span>无需信用卡</span>
        </div>
        <div class="flex items-center">
          <ClockIcon class="w-5 h-5 mr-2 text-green-500" />
          <span>随时取消</span>
        </div>
      </div>

      <!-- Newsletter Signup -->
      <div class="mt-16 max-w-md mx-auto">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
          订阅我们的技术资讯
        </h3>
        <p class="text-gray-600 mb-4">
          获取最新的AI编程技巧、产品更新和行业洞察
        </p>
        <div class="flex gap-2">
          <input
            type="email"
            placeholder="输入您的邮箱地址"
            class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
          <button class="btn-primary px-6 py-3">
            订阅
          </button>
        </div>
        <p class="text-xs text-gray-500 mt-2">
          我们承诺不会发送垃圾邮件，您可以随时取消订阅
        </p>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { Rocket, Calendar, ShieldCheck, CreditCard, Clock } from 'lucide-vue-next'

const RocketIcon = Rocket
const CalendarIcon = Calendar
const ShieldCheckIcon = ShieldCheck
const CreditCardIcon = CreditCard
const ClockIcon = Clock
</script>

<style scoped>
.animation-delay-2000 {
  animation-delay: 2s;
}
</style>
