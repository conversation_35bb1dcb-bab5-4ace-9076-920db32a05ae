<template>
  <nav class="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <div class="flex-shrink-0 flex items-center">
            <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
              <Code2Icon class="w-5 h-5 text-white" />
            </div>
            <span class="ml-2 text-xl font-bold gradient-text">{{ $t('hero.title') }}</span>
          </div>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-8">
            <a href="#features" class="text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors">
              {{ $t('nav.features') }}
            </a>
            <a href="#pricing" class="text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors">
              {{ $t('nav.pricing') }}
            </a>
            <a href="#about" class="text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors">
              {{ $t('nav.about') }}
            </a>
            <a href="#contact" class="text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors">
              {{ $t('nav.contact') }}
            </a>
          </div>
        </div>

        <!-- Language Switcher & CTA Button -->
        <div class="hidden md:flex items-center space-x-4">
          <LanguageSwitcher />
          <button class="btn-primary">
            {{ $t('nav.getStarted') }}
          </button>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button
            @click="toggleMobileMenu"
            class="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
          >
            <MenuIcon v-if="!mobileMenuOpen" class="block h-6 w-6" />
            <XIcon v-else class="block h-6 w-6" />
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile menu -->
    <div v-show="mobileMenuOpen" class="md:hidden">
      <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
        <a href="#features" class="text-gray-700 hover:text-primary-600 block px-3 py-2 text-base font-medium">
          {{ $t('nav.features') }}
        </a>
        <a href="#pricing" class="text-gray-700 hover:text-primary-600 block px-3 py-2 text-base font-medium">
          {{ $t('nav.pricing') }}
        </a>
        <a href="#about" class="text-gray-700 hover:text-primary-600 block px-3 py-2 text-base font-medium">
          {{ $t('nav.about') }}
        </a>
        <a href="#contact" class="text-gray-700 hover:text-primary-600 block px-3 py-2 text-base font-medium">
          {{ $t('nav.contact') }}
        </a>
        <div class="px-3 py-2 space-y-2">
          <LanguageSwitcher />
          <button class="btn-primary w-full">
            {{ $t('nav.getStarted') }}
          </button>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Code2, Menu, X } from 'lucide-vue-next'
import LanguageSwitcher from './LanguageSwitcher.vue'

const Code2Icon = Code2
const MenuIcon = Menu
const XIcon = X

const mobileMenuOpen = ref(false)

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}
</script>
