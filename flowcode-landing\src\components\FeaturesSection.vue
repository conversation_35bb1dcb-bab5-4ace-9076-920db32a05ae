<template>
  <section id="features" class="section-padding bg-white">
    <div class="max-w-7xl mx-auto">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          {{ $t('features.title') }}
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          {{ $t('features.subtitle') }}
        </p>
      </div>

      <!-- Features Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Feature 1: Code Generation -->
        <div class="card group hover:scale-105">
          <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
            <CodeIcon class="w-6 h-6 text-white" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ $t('features.codeGeneration.title') }}</h3>
          <p class="text-gray-600 mb-4">
            {{ $t('features.codeGeneration.description') }}
          </p>
          <ul class="text-sm text-gray-500 space-y-1">
            <li v-for="feature in $t('features.codeGeneration.features')" :key="feature">• {{ feature }}</li>
          </ul>
        </div>

        <!-- Feature 2: Bug Detection -->
        <div class="card group hover:scale-105">
          <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
            <BugIcon class="w-6 h-6 text-white" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ $t('features.bugDetection.title') }}</h3>
          <p class="text-gray-600 mb-4">
            {{ $t('features.bugDetection.description') }}
          </p>
          <ul class="text-sm text-gray-500 space-y-1">
            <li v-for="feature in $t('features.bugDetection.features')" :key="feature">• {{ feature }}</li>
          </ul>
        </div>

        <!-- Feature 3: Auto Testing -->
        <div class="card group hover:scale-105">
          <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
            <TestTubeIcon class="w-6 h-6 text-white" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ $t('features.autoTesting.title') }}</h3>
          <p class="text-gray-600 mb-4">
            {{ $t('features.autoTesting.description') }}
          </p>
          <ul class="text-sm text-gray-500 space-y-1">
            <li v-for="feature in $t('features.autoTesting.features')" :key="feature">• {{ feature }}</li>
          </ul>
        </div>

        <!-- Feature 4: Code Review -->
        <div class="card group hover:scale-105">
          <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
            <EyeIcon class="w-6 h-6 text-white" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ $t('features.codeReview.title') }}</h3>
          <p class="text-gray-600 mb-4">
            {{ $t('features.codeReview.description') }}
          </p>
          <ul class="text-sm text-gray-500 space-y-1">
            <li v-for="feature in $t('features.codeReview.features')" :key="feature">• {{ feature }}</li>
          </ul>
        </div>

        <!-- Feature 5: Documentation -->
        <div class="card group hover:scale-105">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
            <FileTextIcon class="w-6 h-6 text-white" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ $t('features.documentation.title') }}</h3>
          <p class="text-gray-600 mb-4">
            {{ $t('features.documentation.description') }}
          </p>
          <ul class="text-sm text-gray-500 space-y-1">
            <li v-for="feature in $t('features.documentation.features')" :key="feature">• {{ feature }}</li>
          </ul>
        </div>

        <!-- Feature 6: Performance -->
        <div class="card group hover:scale-105">
          <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
            <ZapIcon class="w-6 h-6 text-white" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ $t('features.performance.title') }}</h3>
          <p class="text-gray-600 mb-4">
            {{ $t('features.performance.description') }}
          </p>
          <ul class="text-sm text-gray-500 space-y-1">
            <li v-for="feature in $t('features.performance.features')" :key="feature">• {{ feature }}</li>
          </ul>
        </div>
      </div>

      <!-- Integration Section -->
      <div class="mt-16 text-center">
        <h3 class="text-2xl font-bold text-gray-900 mb-8">{{ $t('features.integration') }}</h3>
        <div class="flex flex-wrap justify-center items-center gap-8 opacity-60">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gray-800 rounded"></div>
            <span class="font-semibold">VS Code</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-blue-600 rounded"></div>
            <span class="font-semibold">IntelliJ</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-green-600 rounded"></div>
            <span class="font-semibold">Sublime</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-purple-600 rounded"></div>
            <span class="font-semibold">Vim</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-orange-600 rounded"></div>
            <span class="font-semibold">Atom</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { Code, Bug, TestTube, Eye, FileText, Zap } from 'lucide-vue-next'

const CodeIcon = Code
const BugIcon = Bug
const TestTubeIcon = TestTube
const EyeIcon = Eye
const FileTextIcon = FileText
const ZapIcon = Zap
</script>
